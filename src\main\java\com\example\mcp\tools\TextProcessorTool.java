package com.example.mcp.tools;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.CallToolResult;
import io.modelcontextprotocol.sdk.types.Tool;
import io.modelcontextprotocol.sdk.types.TextContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文本处理工具
 * 
 * 提供各种文本分析和处理功能
 */
public class TextProcessorTool {
    
    private static final Logger logger = LoggerFactory.getLogger(TextProcessorTool.class);
    
    /**
     * 获取工具规范
     */
    public McpServerFeatures.AsyncToolSpecification getToolSpecification() {
        Tool tool = new Tool(
            "text_processor",
            "Process and analyze text content",
            Map.of(
                "type", "object",
                "properties", Map.of(
                    "operation", Map.of(
                        "type", "string",
                        "enum", new String[]{"count", "analyze", "transform", "search", "validate"},
                        "description", "The text processing operation to perform"
                    ),
                    "text", Map.of(
                        "type", "string",
                        "description", "The text content to process"
                    ),
                    "options", Map.of(
                        "type", "object",
                        "description", "Additional options for the operation",
                        "properties", Map.of(
                            "case_sensitive", Map.of("type", "boolean", "default", false),
                            "pattern", Map.of("type", "string", "description", "Regex pattern for search"),
                            "transform_type", Map.of("type", "string", "enum", new String[]{"uppercase", "lowercase", "title", "reverse"}),
                            "validation_type", Map.of("type", "string", "enum", new String[]{"email", "url", "phone", "json"})
                        )
                    )
                ),
                "required", new String[]{"operation", "text"}
            )
        );
        
        return new McpServerFeatures.AsyncToolSpecification(tool, this::processText);
    }
    
    /**
     * 处理文本
     */
    private Mono<CallToolResult> processText(Object exchange, Map<String, Object> arguments) {
        return Mono.fromCallable(() -> {
            try {
                String operation = (String) arguments.get("operation");
                String text = (String) arguments.get("text");
                @SuppressWarnings("unchecked")
                Map<String, Object> options = (Map<String, Object>) arguments.getOrDefault("options", Map.of());
                
                logger.debug("Processing text with operation: {}", operation);
                
                String result = switch (operation.toLowerCase()) {
                    case "count" -> countText(text);
                    case "analyze" -> analyzeText(text);
                    case "transform" -> transformText(text, options);
                    case "search" -> searchText(text, options);
                    case "validate" -> validateText(text, options);
                    default -> throw new IllegalArgumentException("Unsupported operation: " + operation);
                };
                
                return new CallToolResult(
                    new TextContent("text", result),
                    false
                );
                
            } catch (Exception e) {
                logger.error("Error processing text", e);
                return new CallToolResult(
                    new TextContent("text", "Error: " + e.getMessage()),
                    true
                );
            }
        });
    }
    
    /**
     * 统计文本信息
     */
    private String countText(String text) {
        if (text == null) text = "";
        
        int charCount = text.length();
        int charCountNoSpaces = text.replaceAll("\\s", "").length();
        int wordCount = text.trim().isEmpty() ? 0 : text.trim().split("\\s+").length;
        int lineCount = text.split("\n").length;
        int paragraphCount = text.split("\n\\s*\n").length;
        
        return String.format("""
            Text Statistics:
            Characters: %d
            Characters (no spaces): %d
            Words: %d
            Lines: %d
            Paragraphs: %d
            """, charCount, charCountNoSpaces, wordCount, lineCount, paragraphCount);
    }
    
    /**
     * 分析文本内容
     */
    private String analyzeText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "Text Analysis: Empty or null text provided";
        }
        
        // 基本统计
        String stats = countText(text);
        
        // 字符频率分析
        Map<Character, Long> charFreq = text.chars()
            .mapToObj(c -> (char) c)
            .filter(c -> !Character.isWhitespace(c))
            .collect(Collectors.groupingBy(c -> c, Collectors.counting()));
        
        String topChars = charFreq.entrySet().stream()
            .sorted(Map.Entry.<Character, Long>comparingByValue().reversed())
            .limit(5)
            .map(entry -> entry.getKey() + ": " + entry.getValue())
            .collect(Collectors.joining(", "));
        
        // 单词频率分析
        Map<String, Long> wordFreq = Arrays.stream(text.toLowerCase().split("\\W+"))
            .filter(word -> !word.isEmpty() && word.length() > 2)
            .collect(Collectors.groupingBy(word -> word, Collectors.counting()));
        
        String topWords = wordFreq.entrySet().stream()
            .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
            .limit(5)
            .map(entry -> entry.getKey() + ": " + entry.getValue())
            .collect(Collectors.joining(", "));
        
        return stats + "\nTop Characters: " + topChars + "\nTop Words: " + topWords;
    }
    
    /**
     * 转换文本
     */
    private String transformText(String text, Map<String, Object> options) {
        if (text == null) text = "";
        
        String transformType = (String) options.getOrDefault("transform_type", "uppercase");
        
        String result = switch (transformType.toLowerCase()) {
            case "uppercase" -> text.toUpperCase();
            case "lowercase" -> text.toLowerCase();
            case "title" -> toTitleCase(text);
            case "reverse" -> new StringBuilder(text).reverse().toString();
            default -> throw new IllegalArgumentException("Unsupported transform type: " + transformType);
        };
        
        return "Transformed text (" + transformType + "):\n" + result;
    }
    
    /**
     * 搜索文本
     */
    private String searchText(String text, Map<String, Object> options) {
        if (text == null) text = "";
        
        String pattern = (String) options.get("pattern");
        if (pattern == null || pattern.isEmpty()) {
            throw new IllegalArgumentException("Pattern is required for search operation");
        }
        
        boolean caseSensitive = (Boolean) options.getOrDefault("case_sensitive", false);
        
        try {
            Pattern regex = caseSensitive ? 
                Pattern.compile(pattern) : 
                Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            
            long matchCount = regex.matcher(text).results().count();
            
            String[] lines = text.split("\n");
            StringBuilder matches = new StringBuilder();
            
            for (int i = 0; i < lines.length; i++) {
                if (regex.matcher(lines[i]).find()) {
                    matches.append("Line ").append(i + 1).append(": ").append(lines[i]).append("\n");
                }
            }
            
            return String.format("Search Results:\nPattern: %s\nMatches found: %d\n\nMatching lines:\n%s", 
                pattern, matchCount, matches.toString());
            
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid regex pattern: " + e.getMessage());
        }
    }
    
    /**
     * 验证文本格式
     */
    private String validateText(String text, Map<String, Object> options) {
        if (text == null) text = "";
        
        String validationType = (String) options.getOrDefault("validation_type", "email");
        
        boolean isValid = switch (validationType.toLowerCase()) {
            case "email" -> isValidEmail(text);
            case "url" -> isValidUrl(text);
            case "phone" -> isValidPhone(text);
            case "json" -> isValidJson(text);
            default -> throw new IllegalArgumentException("Unsupported validation type: " + validationType);
        };
        
        return String.format("Validation Result:\nType: %s\nText: %s\nValid: %s", 
            validationType, text, isValid ? "Yes" : "No");
    }
    
    /**
     * 转换为标题格式
     */
    private String toTitleCase(String text) {
        return Arrays.stream(text.split("\\s+"))
            .map(word -> word.isEmpty() ? word : 
                Character.toUpperCase(word.charAt(0)) + word.substring(1).toLowerCase())
            .collect(Collectors.joining(" "));
    }
    
    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return Pattern.matches(emailRegex, email);
    }
    
    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        String urlRegex = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";
        return Pattern.matches(urlRegex, url);
    }
    
    /**
     * 验证电话号码格式
     */
    private boolean isValidPhone(String phone) {
        String phoneRegex = "^[+]?[1-9]\\d{1,14}$";
        return Pattern.matches(phoneRegex, phone.replaceAll("[\\s()-]", ""));
    }
    
    /**
     * 验证JSON格式
     */
    private boolean isValidJson(String json) {
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            mapper.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}

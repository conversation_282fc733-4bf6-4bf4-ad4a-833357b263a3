# Simple MCP Server

一个简单的Java Model Context Protocol (MCP) 服务器实现，提供Tools、Resources和Prompts三大核心功能。

## 项目概述

Model Context Protocol (MCP) 是一个开放协议，用于标准化应用程序如何为大型语言模型(LLM)提供上下文、数据源和工具。本项目实现了一个简单但功能完整的MCP服务器。

## 核心功能

### 🛠️ Tools (工具)
- **计算器** - 基本数学运算
- **文件操作** - 读取、写入文件
- **系统命令** - 执行系统命令
- **文本处理** - 文本分析和处理

### 📚 Resources (资源)
- **系统信息** - CPU、内存、磁盘使用情况
- **文件内容** - 读取和暴露文件内容
- **配置信息** - 应用配置和环境变量
- **日志文件** - 应用日志访问

### 💬 Prompts (提示模板)
- **代码审查** - 代码质量分析模板
- **文档生成** - API文档生成模板
- **错误诊断** - 问题诊断和解决方案模板
- **性能分析** - 系统性能分析模板

## 技术栈

- **Java 17+** - 现代Java特性
- **Spring Boot 3.2** - 应用框架
- **Spring WebFlux** - 响应式Web框架
- **MCP Java SDK 0.10.0** - 官方MCP SDK
- **Maven** - 项目构建工具
- **Jackson** - JSON处理
- **SLF4J + Logback** - 日志框架

## 项目结构

```
simple-mcp-server/
├── pom.xml                                 # Maven配置文件
├── README.md                              # 项目说明文档
├── src/main/java/com/example/mcp/
│   ├── SimpleMcpServerApplication.java    # 主应用类
│   ├── config/
│   │   └── McpServerConfig.java          # MCP服务器配置
│   ├── tools/                            # 工具实现
│   ├── resources/                        # 资源实现
│   ├── prompts/                          # 提示模板实现
│   └── service/                          # 业务服务层
├── src/main/resources/
│   ├── application.yml                   # 应用配置
│   └── logback-spring.xml               # 日志配置
└── src/test/                            # 测试代码
```

## 快速开始

### 前置要求

- Java 17 或更高版本
- Maven 3.6 或更高版本

### 构建和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd simple-mcp-server
```

2. **构建项目**
```bash
mvn clean compile
```

3. **运行服务器**
```bash
mvn spring-boot:run
```

4. **验证服务**
服务器启动后，访问：
- 健康检查: http://localhost:8080/actuator/health
- MCP端点: http://localhost:8080/mcp/message

### 配置说明

主要配置在 `src/main/resources/application.yml` 中：

```yaml
server:
  port: 8080                    # 服务端口

mcp:
  server:
    name: "Simple MCP Server"   # 服务器名称
    version: "1.0.0"           # 版本号
  transport:
    endpoint: "/mcp/message"    # MCP消息端点
```

## API文档

### MCP协议端点

- **POST** `/mcp/message` - MCP协议消息处理端点（SSE）

### 管理端点

- **GET** `/actuator/health` - 健康检查
- **GET** `/actuator/info` - 应用信息
- **GET** `/actuator/metrics` - 应用指标

## 开发指南

### 添加新工具

1. 在 `tools` 包中创建工具类
2. 实现工具逻辑
3. 在配置中注册工具

### 添加新资源

1. 在 `resources` 包中创建资源类
2. 实现资源读取逻辑
3. 在配置中注册资源

### 添加新提示模板

1. 在 `prompts` 包中创建提示类
2. 定义提示模板和参数
3. 在配置中注册提示

## 测试

```bash
# 运行所有测试
mvn test

# 运行特定测试
mvn test -Dtest=McpServerConfigTest
```

## 部署

### JAR包部署

```bash
# 构建JAR包
mvn clean package

# 运行JAR包
java -jar target/simple-mcp-server-1.0.0.jar
```

### Docker部署

```bash
# 构建Docker镜像
docker build -t simple-mcp-server .

# 运行容器
docker run -p 8080:8080 simple-mcp-server
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件至 [<EMAIL>]

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基本的Tools、Resources、Prompts功能
- 支持WebFlux SSE传输
- 完整的Spring Boot集成

package com.example.mcp.prompts;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 错误诊断提示模板
 * 
 * 提供错误分析和解决方案建议的提示模板
 */
public class ErrorDiagnosisPrompt {
    
    private static final Logger logger = LoggerFactory.getLogger(ErrorDiagnosisPrompt.class);
    
    /**
     * 获取提示规范
     */
    public McpServerFeatures.AsyncPromptSpecification getPromptSpecification() {
        Prompt prompt = new Prompt(
            "error_diagnosis",
            "Error Diagnosis and Troubleshooting",
            "Analyze errors, exceptions, and issues to provide diagnosis and solutions",
            List.of(
                new PromptArgument(
                    "error_info",
                    "Error message, stack trace, or problem description",
                    true
                ),
                new PromptArgument(
                    "context",
                    "Additional context (environment, recent changes, related code)",
                    false
                ),
                new PromptArgument(
                    "system_type",
                    "Type of system (web_app, desktop_app, mobile_app, api, database, etc.)",
                    false
                ),
                new PromptArgument(
                    "urgency",
                    "Urgency level (low, medium, high, critical)",
                    false
                )
            )
        );
        
        return new McpServerFeatures.AsyncPromptSpecification(prompt, this::generateErrorDiagnosisPrompt);
    }
    
    /**
     * 生成错误诊断提示
     */
    private Mono<GetPromptResult> generateErrorDiagnosisPrompt(Object exchange, GetPromptRequest request) {
        return Mono.fromCallable(() -> {
            try {
                Map<String, String> arguments = request.arguments();
                
                String errorInfo = arguments.get("error_info");
                String context = arguments.getOrDefault("context", "No additional context provided");
                String systemType = arguments.getOrDefault("system_type", "general application");
                String urgency = arguments.getOrDefault("urgency", "medium");
                
                logger.debug("Generating error diagnosis prompt for system: {}, urgency: {}", 
                    systemType, urgency);
                
                if (errorInfo == null || errorInfo.trim().isEmpty()) {
                    throw new IllegalArgumentException("Error info parameter is required and cannot be empty");
                }
                
                String promptText = generateDiagnosisPromptText(errorInfo, context, systemType, urgency);
                
                PromptMessage message = new PromptMessage(
                    "user",
                    new TextContent("text", promptText)
                );
                
                return new GetPromptResult(
                    String.format("Error diagnosis prompt for %s (%s urgency)", systemType, urgency),
                    List.of(message)
                );
                
            } catch (Exception e) {
                logger.error("Error generating error diagnosis prompt", e);
                
                PromptMessage errorMessage = new PromptMessage(
                    "user",
                    new TextContent("text", "Error generating error diagnosis prompt: " + e.getMessage())
                );
                
                return new GetPromptResult(
                    "Error in error diagnosis prompt",
                    List.of(errorMessage)
                );
            }
        });
    }
    
    /**
     * 生成错误诊断提示文本
     */
    private String generateDiagnosisPromptText(String errorInfo, String context, String systemType, String urgency) {
        String urgencyNote = getUrgencyNote(urgency);
        String systemSpecificGuidance = getSystemSpecificGuidance(systemType);
        
        return String.format("""
            Please analyze the following error/issue and provide a comprehensive diagnosis and solution plan.
            
            **System Information:**
            - System Type: %s
            - Urgency Level: %s
            %s
            
            **Error/Issue Details:**
            ```
            %s
            ```
            
            **Additional Context:**
            %s
            
            **Please provide a structured analysis including:**
            
            1. **Error Classification**
               - Type of error (syntax, runtime, logic, configuration, etc.)
               - Severity assessment
               - Potential impact on system/users
            
            2. **Root Cause Analysis**
               - Most likely cause(s) of the error
               - Contributing factors
               - Why this error occurred
            
            3. **Immediate Actions** (if urgency is high/critical)
               - Quick fixes or workarounds
               - Steps to minimize impact
               - Emergency mitigation strategies
            
            4. **Detailed Diagnosis**
               - Step-by-step analysis of the error
               - Related components that might be affected
               - Dependencies and interconnections to consider
            
            5. **Solution Recommendations**
               - Primary solution approach
               - Alternative solutions
               - Step-by-step implementation guide
               - Code fixes (if applicable)
            
            6. **Prevention Strategies**
               - How to prevent this error in the future
               - Monitoring and alerting recommendations
               - Code review or process improvements
            
            7. **Testing and Validation**
               - How to test the fix
               - Validation criteria
               - Regression testing considerations
            
            %s
            
            **Additional Considerations:**
            - Provide specific, actionable recommendations
            - Include relevant code examples or configuration changes
            - Consider backward compatibility and side effects
            - Suggest monitoring or logging improvements
            - Recommend documentation updates if needed
            
            Please be thorough but practical in your analysis and recommendations.
            """, systemType, urgency, urgencyNote, errorInfo, context, systemSpecificGuidance);
    }
    
    /**
     * 获取紧急程度说明
     */
    private String getUrgencyNote(String urgency) {
        return switch (urgency.toLowerCase()) {
            case "critical" -> """
                
                ⚠️  **CRITICAL URGENCY**: This is a critical issue requiring immediate attention.
                Focus on immediate stabilization and quick fixes first, then comprehensive solutions.
                """;
            case "high" -> """
                
                🔴 **HIGH URGENCY**: This issue needs prompt resolution.
                Prioritize solutions that can be implemented quickly while ensuring stability.
                """;
            case "medium" -> """
                
                🟡 **MEDIUM URGENCY**: Standard priority issue.
                Balance thoroughness with reasonable implementation timeline.
                """;
            case "low" -> """
                
                🟢 **LOW URGENCY**: Non-critical issue.
                Focus on comprehensive, well-tested solutions and prevention strategies.
                """;
            default -> "";
        };
    }
    
    /**
     * 获取系统特定的指导
     */
    private String getSystemSpecificGuidance(String systemType) {
        return switch (systemType.toLowerCase()) {
            case "web_app", "web_application" -> """
                8. **Web Application Specific Considerations**
                   - Browser compatibility issues
                   - Network connectivity problems
                   - Session management and authentication
                   - Performance and loading issues
                   - Security vulnerabilities (XSS, CSRF, etc.)
                """;
            
            case "api", "rest_api", "web_api" -> """
                8. **API Specific Considerations**
                   - HTTP status codes and error responses
                   - Request/response format issues
                   - Authentication and authorization problems
                   - Rate limiting and throttling
                   - API versioning and backward compatibility
                """;
            
            case "database", "db" -> """
                8. **Database Specific Considerations**
                   - Query performance and optimization
                   - Index usage and table locks
                   - Connection pool issues
                   - Data integrity and constraints
                   - Backup and recovery implications
                """;
            
            case "mobile_app", "mobile" -> """
                8. **Mobile Application Specific Considerations**
                   - Platform-specific issues (iOS/Android)
                   - Device compatibility and resources
                   - Network connectivity and offline scenarios
                   - App store compliance and deployment
                   - Battery and performance optimization
                """;
            
            case "desktop_app", "desktop" -> """
                8. **Desktop Application Specific Considerations**
                   - Operating system compatibility
                   - File system permissions and access
                   - System resources and dependencies
                   - Installation and deployment issues
                   - User environment variations
                """;
            
            default -> """
                8. **General System Considerations**
                   - Environment-specific factors
                   - Configuration and setup issues
                   - Resource constraints and limitations
                   - Integration and dependency problems
                   - Deployment and operational concerns
                """;
        };
    }
}

version: '3.8'

services:
  # Simple MCP Server 服务
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    image: simple-mcp-server:1.0.0
    container_name: simple-mcp-server
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "8080:8080"
    
    # 环境变量
    environment:
      - JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=8080
      - LOGGING_LEVEL_COM_EXAMPLE_MCP=DEBUG
    
    # 数据卷挂载
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./data:/app/data
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - mcp-network
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: mcp-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    
    depends_on:
      mcp-server:
        condition: service_healthy
    
    networks:
      - mcp-network
    
    # 仅在需要时启用
    profiles:
      - with-nginx

  # Prometheus监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: mcp-prometheus
    restart: unless-stopped
    
    ports:
      - "9090:9090"
    
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    networks:
      - mcp-network
    
    # 仅在需要时启用
    profiles:
      - with-monitoring

  # Grafana仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: mcp-grafana
    restart: unless-stopped
    
    ports:
      - "3000:3000"
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    depends_on:
      - prometheus
    
    networks:
      - mcp-network
    
    # 仅在需要时启用
    profiles:
      - with-monitoring

# 网络配置
networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# Simple MCP Server Dockerfile
# 基于OpenJDK 17构建轻量级的MCP服务器镜像

# 使用官方OpenJDK 17镜像作为基础镜像
FROM openjdk:17-jdk-slim

# 设置维护者信息
LABEL maintainer="Simple MCP Server Team"
LABEL description="A simple Model Context Protocol server implementation in Java"
LABEL version="1.0.0"

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r mcpuser && useradd -r -g mcpuser mcpuser

# 安装必要的系统包
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        wget \
        ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# 创建应用目录结构
RUN mkdir -p /app/logs /app/config /app/data && \
    chown -R mcpuser:mcpuser /app

# 复制JAR文件
COPY target/simple-mcp-server-1.0.0.jar /app/simple-mcp-server.jar

# 复制配置文件
COPY src/main/resources/application.yml /app/config/application.yml

# 设置文件权限
RUN chown mcpuser:mcpuser /app/simple-mcp-server.jar /app/config/application.yml

# 切换到非root用户
USER mcpuser

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport"
ENV SPRING_PROFILES_ACTIVE="docker"
ENV SERVER_PORT=8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${SERVER_PORT}/api/health || exit 1

# 设置启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/simple-mcp-server.jar --spring.config.location=classpath:/application.yml,file:/app/config/application.yml"]

# 添加标签
LABEL org.opencontainers.image.title="Simple MCP Server"
LABEL org.opencontainers.image.description="A simple Model Context Protocol server implementation"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Simple MCP Server Team"
LABEL org.opencontainers.image.licenses="MIT"

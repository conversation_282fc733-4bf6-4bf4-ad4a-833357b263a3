server:
  port: 0  # 使用随机端口进行测试

spring:
  application:
    name: simple-mcp-server-test

# MCP服务器测试配置
mcp:
  server:
    name: "Simple MCP Server Test"
    version: "1.0.0-TEST"
    description: "Test configuration for Simple MCP Server"
    
    capabilities:
      tools: true
      resources: true
      prompts: true
      logging: true
      
    transport:
      type: "sse"
      endpoint: "/mcp/message"

# 测试日志配置
logging:
  level:
    com.example.mcp: DEBUG
    io.modelcontextprotocol: DEBUG
    org.springframework.web: WARN
    reactor.netty: WARN
    org.springframework.test: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 禁用管理端点以简化测试
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true

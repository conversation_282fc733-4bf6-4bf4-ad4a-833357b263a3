package com.example.mcp.prompts;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 文档生成提示模板
 * 
 * 提供API文档、代码文档等生成的提示模板
 */
public class DocumentationPrompt {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentationPrompt.class);
    
    /**
     * 获取提示规范
     */
    public McpServerFeatures.AsyncPromptSpecification getPromptSpecification() {
        Prompt prompt = new Prompt(
            "generate_documentation",
            "Generate Documentation",
            "Generate comprehensive documentation for code, APIs, or projects",
            List.of(
                new PromptArgument(
                    "content",
                    "The code, API, or project content to document",
                    true
                ),
                new PromptArgument(
                    "type",
                    "Type of documentation (api, code, readme, user_guide, technical_spec)",
                    false
                ),
                new PromptArgument(
                    "format",
                    "Output format (markdown, html, javadoc, sphinx)",
                    false
                ),
                new PromptArgument(
                    "audience",
                    "Target audience (developers, end_users, administrators)",
                    false
                )
            )
        );
        
        return new McpServerFeatures.AsyncPromptSpecification(prompt, this::generateDocumentationPrompt);
    }
    
    /**
     * 生成文档生成提示
     */
    private Mono<GetPromptResult> generateDocumentationPrompt(Object exchange, GetPromptRequest request) {
        return Mono.fromCallable(() -> {
            try {
                Map<String, String> arguments = request.arguments();
                
                String content = arguments.get("content");
                String type = arguments.getOrDefault("type", "code");
                String format = arguments.getOrDefault("format", "markdown");
                String audience = arguments.getOrDefault("audience", "developers");
                
                logger.debug("Generating documentation prompt for type: {}, format: {}, audience: {}", 
                    type, format, audience);
                
                if (content == null || content.trim().isEmpty()) {
                    throw new IllegalArgumentException("Content parameter is required and cannot be empty");
                }
                
                String promptText = generateDocPromptText(content, type, format, audience);
                
                PromptMessage message = new PromptMessage(
                    "user",
                    new TextContent("text", promptText)
                );
                
                return new GetPromptResult(
                    String.format("Documentation generation prompt (%s, %s format, for %s)", 
                        type, format, audience),
                    List.of(message)
                );
                
            } catch (Exception e) {
                logger.error("Error generating documentation prompt", e);
                
                PromptMessage errorMessage = new PromptMessage(
                    "user",
                    new TextContent("text", "Error generating documentation prompt: " + e.getMessage())
                );
                
                return new GetPromptResult(
                    "Error in documentation prompt",
                    List.of(errorMessage)
                );
            }
        });
    }
    
    /**
     * 生成文档提示文本
     */
    private String generateDocPromptText(String content, String type, String format, String audience) {
        String basePrompt = String.format("""
            Please generate comprehensive %s documentation for the following content, formatted as %s and targeted at %s.
            
            **Documentation Requirements:**
            
            %s
            
            **Content to Document:**
            
            ```
            %s
            ```
            
            **Output Guidelines:**
            
            %s
            
            Please ensure the documentation is:
            - Clear and well-structured
            - Appropriate for the target audience (%s)
            - Complete and accurate
            - Following %s formatting standards
            - Including relevant examples where helpful
            """, type, format, audience, 
            getTypeSpecificRequirements(type),
            content,
            getFormatSpecificGuidelines(format),
            audience, format);
        
        return basePrompt;
    }
    
    /**
     * 获取特定类型的文档要求
     */
    private String getTypeSpecificRequirements(String type) {
        return switch (type.toLowerCase()) {
            case "api" -> """
                1. **API Overview**: Brief description of the API's purpose and functionality
                2. **Endpoints**: List all endpoints with HTTP methods, paths, and descriptions
                3. **Parameters**: Document all request parameters (path, query, body)
                4. **Request/Response Examples**: Provide sample requests and responses
                5. **Error Codes**: Document possible error responses and their meanings
                6. **Authentication**: Explain authentication requirements if any
                7. **Rate Limiting**: Document any rate limiting policies
                """;
            
            case "code" -> """
                1. **Class/Function Overview**: Purpose and responsibility of each component
                2. **Parameters**: Document all input parameters with types and descriptions
                3. **Return Values**: Explain what the code returns and when
                4. **Exceptions**: Document any exceptions that might be thrown
                5. **Usage Examples**: Provide practical usage examples
                6. **Dependencies**: List any external dependencies or requirements
                7. **Performance Notes**: Any performance considerations or limitations
                """;
            
            case "readme" -> """
                1. **Project Description**: Clear explanation of what the project does
                2. **Installation Instructions**: Step-by-step setup guide
                3. **Usage Examples**: Basic usage examples and common use cases
                4. **Configuration**: How to configure the project
                5. **Contributing Guidelines**: How others can contribute
                6. **License Information**: License details and terms
                7. **Support/Contact**: How to get help or report issues
                """;
            
            case "user_guide" -> """
                1. **Getting Started**: Quick start guide for new users
                2. **Feature Overview**: Comprehensive list of features and capabilities
                3. **Step-by-Step Tutorials**: Detailed walkthroughs for common tasks
                4. **Troubleshooting**: Common issues and their solutions
                5. **FAQ**: Frequently asked questions and answers
                6. **Best Practices**: Recommended approaches and tips
                7. **Advanced Features**: Documentation for power users
                """;
            
            case "technical_spec" -> """
                1. **System Architecture**: High-level system design and components
                2. **Technical Requirements**: Hardware, software, and system requirements
                3. **Data Models**: Database schemas and data structures
                4. **Integration Points**: External systems and APIs
                5. **Security Considerations**: Security measures and protocols
                6. **Performance Specifications**: Expected performance metrics
                7. **Deployment Architecture**: How the system should be deployed
                """;
            
            default -> """
                1. **Overview**: Clear description of the subject matter
                2. **Key Components**: Main elements and their relationships
                3. **Usage/Implementation**: How to use or implement
                4. **Examples**: Practical examples and use cases
                5. **Best Practices**: Recommended approaches
                6. **Limitations**: Known limitations or constraints
                7. **Additional Resources**: Links to related documentation
                """;
        };
    }
    
    /**
     * 获取特定格式的指导原则
     */
    private String getFormatSpecificGuidelines(String format) {
        return switch (format.toLowerCase()) {
            case "markdown" -> """
                - Use proper Markdown syntax with headers (# ## ###)
                - Include code blocks with language specification
                - Use tables for structured data
                - Add links and references where appropriate
                - Use bullet points and numbered lists for clarity
                """;
            
            case "html" -> """
                - Use semantic HTML elements (header, section, article, etc.)
                - Include proper heading hierarchy (h1, h2, h3)
                - Use code and pre tags for code examples
                - Add CSS classes for styling hooks
                - Include navigation elements if appropriate
                """;
            
            case "javadoc" -> """
                - Use proper Javadoc tags (@param, @return, @throws, etc.)
                - Include @since and @version tags where appropriate
                - Use {@code} for inline code references
                - Add @see tags for related methods/classes
                - Follow standard Javadoc formatting conventions
                """;
            
            case "sphinx" -> """
                - Use reStructuredText syntax
                - Include proper directives (.. code-block::, .. note::, etc.)
                - Use cross-references with :ref: and :doc:
                - Add index entries where appropriate
                - Follow Sphinx documentation conventions
                """;
            
            default -> """
                - Use clear, consistent formatting
                - Organize content with appropriate headings
                - Include code examples in readable format
                - Use consistent indentation and spacing
                - Add visual separators between sections
                """;
        };
    }
}

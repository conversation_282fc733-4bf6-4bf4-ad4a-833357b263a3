package com.example.mcp.service;

import com.example.mcp.prompts.CodeReviewPrompt;
import com.example.mcp.prompts.DocumentationPrompt;
import com.example.mcp.prompts.ErrorDiagnosisPrompt;
import com.example.mcp.prompts.PerformanceAnalysisPrompt;
import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 提示服务类
 * 
 * 管理所有MCP提示模板的注册和提供
 */
@Service
public class PromptService {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptService.class);
    
    private final List<McpServerFeatures.AsyncPromptSpecification> prompts;
    
    public PromptService() {
        this.prompts = new ArrayList<>();
        initializePrompts();
    }
    
    /**
     * 初始化所有提示模板
     */
    private void initializePrompts() {
        logger.info("Initializing MCP prompts...");
        
        // 添加代码审查提示
        prompts.add(new CodeReviewPrompt().getPromptSpecification());
        
        // 添加文档生成提示
        prompts.add(new DocumentationPrompt().getPromptSpecification());
        
        // 添加错误诊断提示
        prompts.add(new ErrorDiagnosisPrompt().getPromptSpecification());
        
        // 添加性能分析提示
        prompts.add(new PerformanceAnalysisPrompt().getPromptSpecification());
        
        logger.info("Initialized {} prompts", prompts.size());
    }
    
    /**
     * 获取所有提示模板
     */
    public List<McpServerFeatures.AsyncPromptSpecification> getAllPrompts() {
        return new ArrayList<>(prompts);
    }
    
    /**
     * 获取提示模板数量
     */
    public int getPromptCount() {
        return prompts.size();
    }
    
    /**
     * 根据名称查找提示模板
     */
    public McpServerFeatures.AsyncPromptSpecification findPromptByName(String name) {
        return prompts.stream()
            .filter(prompt -> prompt.prompt().name().equals(name))
            .findFirst()
            .orElse(null);
    }
}

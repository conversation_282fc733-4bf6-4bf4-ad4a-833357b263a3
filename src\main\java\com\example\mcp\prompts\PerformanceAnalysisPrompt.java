package com.example.mcp.prompts;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 性能分析提示模板
 * 
 * 提供系统性能分析和优化建议的提示模板
 */
public class PerformanceAnalysisPrompt {
    
    private static final Logger logger = LoggerFactory.getLogger(PerformanceAnalysisPrompt.class);
    
    /**
     * 获取提示规范
     */
    public McpServerFeatures.AsyncPromptSpecification getPromptSpecification() {
        Prompt prompt = new Prompt(
            "performance_analysis",
            "Performance Analysis and Optimization",
            "Analyze system performance metrics and provide optimization recommendations",
            List.of(
                new PromptArgument(
                    "performance_data",
                    "Performance metrics, logs, or profiling data",
                    true
                ),
                new PromptArgument(
                    "system_info",
                    "System information (hardware, software, configuration)",
                    false
                ),
                new PromptArgument(
                    "analysis_type",
                    "Type of analysis (cpu, memory, network, database, application)",
                    false
                ),
                new PromptArgument(
                    "target_metrics",
                    "Target performance goals or SLA requirements",
                    false
                )
            )
        );
        
        return new McpServerFeatures.AsyncPromptSpecification(prompt, this::generatePerformanceAnalysisPrompt);
    }
    
    /**
     * 生成性能分析提示
     */
    private Mono<GetPromptResult> generatePerformanceAnalysisPrompt(Object exchange, GetPromptRequest request) {
        return Mono.fromCallable(() -> {
            try {
                Map<String, String> arguments = request.arguments();
                
                String performanceData = arguments.get("performance_data");
                String systemInfo = arguments.getOrDefault("system_info", "No system information provided");
                String analysisType = arguments.getOrDefault("analysis_type", "general");
                String targetMetrics = arguments.getOrDefault("target_metrics", "No specific targets provided");
                
                logger.debug("Generating performance analysis prompt for type: {}", analysisType);
                
                if (performanceData == null || performanceData.trim().isEmpty()) {
                    throw new IllegalArgumentException("Performance data parameter is required and cannot be empty");
                }
                
                String promptText = generateAnalysisPromptText(performanceData, systemInfo, analysisType, targetMetrics);
                
                PromptMessage message = new PromptMessage(
                    "user",
                    new TextContent("text", promptText)
                );
                
                return new GetPromptResult(
                    String.format("Performance analysis prompt (%s analysis)", analysisType),
                    List.of(message)
                );
                
            } catch (Exception e) {
                logger.error("Error generating performance analysis prompt", e);
                
                PromptMessage errorMessage = new PromptMessage(
                    "user",
                    new TextContent("text", "Error generating performance analysis prompt: " + e.getMessage())
                );
                
                return new GetPromptResult(
                    "Error in performance analysis prompt",
                    List.of(errorMessage)
                );
            }
        });
    }
    
    /**
     * 生成性能分析提示文本
     */
    private String generateAnalysisPromptText(String performanceData, String systemInfo, String analysisType, String targetMetrics) {
        String specificGuidance = getAnalysisTypeGuidance(analysisType);
        
        return String.format("""
            Please perform a comprehensive performance analysis based on the provided data and system information.
            
            **Analysis Type:** %s
            
            **Performance Data:**
            ```
            %s
            ```
            
            **System Information:**
            %s
            
            **Target Performance Goals:**
            %s
            
            **Please provide a detailed analysis including:**
            
            1. **Current Performance Assessment**
               - Key metrics summary and trends
               - Performance baseline establishment
               - Identification of performance patterns
               - Comparison with industry standards or best practices
            
            2. **Bottleneck Identification**
               - Primary performance bottlenecks
               - Secondary performance issues
               - Resource utilization analysis
               - Dependency and interaction effects
            
            3. **Root Cause Analysis**
               - Underlying causes of performance issues
               - Contributing factors and conditions
               - System design or configuration problems
               - Code-level performance issues (if applicable)
            
            4. **Impact Assessment**
               - User experience impact
               - Business impact and cost implications
               - System stability and reliability effects
               - Scalability limitations
            
            5. **Optimization Recommendations**
               - High-impact, low-effort improvements (quick wins)
               - Medium-term optimization strategies
               - Long-term architectural improvements
               - Resource allocation recommendations
            
            %s
            
            7. **Implementation Roadmap**
               - Prioritized list of optimizations
               - Implementation complexity and effort estimates
               - Dependencies and prerequisites
               - Risk assessment for each optimization
            
            8. **Monitoring and Measurement**
               - Key performance indicators (KPIs) to track
               - Monitoring tools and techniques
               - Performance testing strategies
               - Alerting and threshold recommendations
            
            9. **Expected Outcomes**
               - Projected performance improvements
               - Resource savings estimates
               - ROI analysis for optimization efforts
               - Timeline for achieving target metrics
            
            **Analysis Guidelines:**
            - Use specific metrics and quantitative analysis where possible
            - Provide actionable, prioritized recommendations
            - Consider both immediate fixes and long-term improvements
            - Include cost-benefit analysis for major changes
            - Address scalability and future growth considerations
            - Suggest performance testing and validation approaches
            
            Please be thorough in your analysis and practical in your recommendations.
            """, analysisType, performanceData, systemInfo, targetMetrics, specificGuidance);
    }
    
    /**
     * 获取特定分析类型的指导
     */
    private String getAnalysisTypeGuidance(String analysisType) {
        return switch (analysisType.toLowerCase()) {
            case "cpu" -> """
                6. **CPU-Specific Analysis**
                   - CPU utilization patterns and spikes
                   - Process and thread analysis
                   - Context switching overhead
                   - CPU-bound vs I/O-bound operations
                   - Multi-core utilization efficiency
                   - CPU cache performance
                   - Instruction-level optimizations
                """;
            
            case "memory" -> """
                6. **Memory-Specific Analysis**
                   - Memory usage patterns and leaks
                   - Garbage collection performance (if applicable)
                   - Memory allocation and deallocation efficiency
                   - Cache hit/miss ratios
                   - Virtual memory and paging behavior
                   - Memory fragmentation issues
                   - Buffer and pool management
                """;
            
            case "network" -> """
                6. **Network-Specific Analysis**
                   - Network latency and throughput
                   - Bandwidth utilization and congestion
                   - Connection pooling and management
                   - Protocol efficiency and overhead
                   - Network I/O patterns
                   - DNS resolution performance
                   - Load balancing effectiveness
                """;
            
            case "database" -> """
                6. **Database-Specific Analysis**
                   - Query performance and execution plans
                   - Index usage and optimization
                   - Connection pool management
                   - Transaction isolation and locking
                   - Database cache performance
                   - I/O patterns and disk usage
                   - Replication and sharding efficiency
                """;
            
            case "application" -> """
                6. **Application-Specific Analysis**
                   - Application response times
                   - Request processing efficiency
                   - Resource utilization per operation
                   - Caching strategies and effectiveness
                   - Algorithm complexity and optimization
                   - Framework and library performance
                   - Application architecture efficiency
                """;
            
            default -> """
                6. **General Performance Analysis**
                   - Overall system resource utilization
                   - Component interaction efficiency
                   - Scalability characteristics
                   - Performance under different load conditions
                   - System architecture optimization opportunities
                   - Configuration and tuning recommendations
                   - Technology stack evaluation
                """;
        };
    }
}

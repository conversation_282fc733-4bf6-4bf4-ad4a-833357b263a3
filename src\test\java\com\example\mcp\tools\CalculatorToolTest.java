package com.example.mcp.tools;

import io.modelcontextprotocol.sdk.types.CallToolResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 计算器工具测试
 */
class CalculatorToolTest {

    private CalculatorTool calculatorTool;

    @BeforeEach
    void setUp() {
        calculatorTool = new CalculatorTool();
    }

    @Test
    void testAddition() {
        Map<String, Object> arguments = Map.of(
            "operation", "add",
            "a", 5.0,
            "b", 3.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("8.000000"));
            })
            .verifyComplete();
    }

    @Test
    void testSubtraction() {
        Map<String, Object> arguments = Map.of(
            "operation", "subtract",
            "a", 10.0,
            "b", 4.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("6.000000"));
            })
            .verifyComplete();
    }

    @Test
    void testMultiplication() {
        Map<String, Object> arguments = Map.of(
            "operation", "multiply",
            "a", 6.0,
            "b", 7.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("42.000000"));
            })
            .verifyComplete();
    }

    @Test
    void testDivision() {
        Map<String, Object> arguments = Map.of(
            "operation", "divide",
            "a", 15.0,
            "b", 3.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("5.000000"));
            })
            .verifyComplete();
    }

    @Test
    void testDivisionByZero() {
        Map<String, Object> arguments = Map.of(
            "operation", "divide",
            "a", 10.0,
            "b", 0.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertTrue(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Division by zero"));
            })
            .verifyComplete();
    }

    @Test
    void testSquareRoot() {
        Map<String, Object> arguments = Map.of(
            "operation", "sqrt",
            "a", 16.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("4.000000"));
            })
            .verifyComplete();
    }

    @Test
    void testPower() {
        Map<String, Object> arguments = Map.of(
            "operation", "power",
            "a", 2.0,
            "b", 3.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("8.000000"));
            })
            .verifyComplete();
    }

    @Test
    void testInvalidOperation() {
        Map<String, Object> arguments = Map.of(
            "operation", "invalid",
            "a", 5.0,
            "b", 3.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertTrue(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Unsupported operation"));
            })
            .verifyComplete();
    }

    @Test
    void testMissingSecondNumber() {
        Map<String, Object> arguments = Map.of(
            "operation", "add",
            "a", 5.0
        );

        Mono<CallToolResult> result = calculatorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertTrue(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Second number required"));
            })
            .verifyComplete();
    }
}

package com.example.mcp.resources;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.ReadResourceRequest;
import io.modelcontextprotocol.sdk.types.ReadResourceResult;
import io.modelcontextprotocol.sdk.types.Resource;
import io.modelcontextprotocol.sdk.types.TextResourceContents;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件内容资源
 * 
 * 提供文件内容访问功能（限制在工作目录内）
 */
public class FileContentResource {
    
    private static final Logger logger = LoggerFactory.getLogger(FileContentResource.class);
    
    private static final String RESOURCE_URI_PREFIX = "file://";
    private static final String WORK_DIR = System.getProperty("user.dir");
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    
    /**
     * 获取资源规范
     */
    public McpServerFeatures.AsyncResourceSpecification getResourceSpecification() {
        Resource resource = new Resource(
            RESOURCE_URI_PREFIX + "README.md",
            "File Content",
            "Access to file contents within the working directory",
            "text/plain"
        );
        
        return new McpServerFeatures.AsyncResourceSpecification(resource, this::readFileContent);
    }
    
    /**
     * 读取文件内容
     */
    private Mono<ReadResourceResult> readFileContent(Object exchange, ReadResourceRequest request) {
        return Mono.fromCallable(() -> {
            try {
                String uri = request.uri();
                logger.debug("Reading file content resource: {}", uri);
                
                if (!uri.startsWith(RESOURCE_URI_PREFIX)) {
                    throw new IllegalArgumentException("Invalid file URI: " + uri);
                }
                
                String relativePath = uri.substring(RESOURCE_URI_PREFIX.length());
                Path filePath = validateAndResolvePath(relativePath);
                
                String content = readFileWithSizeLimit(filePath);
                String mimeType = determineMimeType(filePath);
                
                TextResourceContents contents = new TextResourceContents(
                    uri,
                    mimeType,
                    content
                );
                
                return new ReadResourceResult(contents);
                
            } catch (Exception e) {
                logger.error("Error reading file content", e);
                
                String errorContent = String.format("""
                    Error reading file: %s
                    Message: %s
                    Timestamp: %s
                    """, request.uri(), e.getMessage(), 
                    LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                
                TextResourceContents errorContents = new TextResourceContents(
                    request.uri(),
                    "text/plain",
                    errorContent
                );
                
                return new ReadResourceResult(errorContents);
            }
        });
    }
    
    /**
     * 验证并解析文件路径
     */
    private Path validateAndResolvePath(String relativePath) throws IOException {
        if (relativePath == null || relativePath.trim().isEmpty()) {
            throw new IllegalArgumentException("File path cannot be empty");
        }
        
        // 移除可能的路径遍历攻击
        if (relativePath.contains("..") || relativePath.startsWith("/")) {
            throw new SecurityException("Path traversal not allowed: " + relativePath);
        }
        
        Path workDir = Paths.get(WORK_DIR);
        Path filePath = workDir.resolve(relativePath).normalize();
        
        // 确保解析后的路径仍在工作目录内
        if (!filePath.startsWith(workDir)) {
            throw new SecurityException("Access outside working directory not allowed: " + filePath);
        }
        
        if (!Files.exists(filePath)) {
            throw new IOException("File does not exist: " + filePath);
        }
        
        if (!Files.isRegularFile(filePath)) {
            throw new IOException("Path is not a regular file: " + filePath);
        }
        
        if (!Files.isReadable(filePath)) {
            throw new IOException("File is not readable: " + filePath);
        }
        
        return filePath;
    }
    
    /**
     * 读取文件内容（带大小限制）
     */
    private String readFileWithSizeLimit(Path filePath) throws IOException {
        long fileSize = Files.size(filePath);
        
        if (fileSize > MAX_FILE_SIZE) {
            throw new IOException(String.format(
                "File too large (max %d bytes): %d bytes", MAX_FILE_SIZE, fileSize));
        }
        
        try {
            return Files.readString(filePath);
        } catch (Exception e) {
            // 如果无法以UTF-8读取，尝试读取为二进制并提供摘要
            byte[] bytes = Files.readAllBytes(filePath);
            return String.format("""
                [Binary file detected - %d bytes]
                File: %s
                Size: %s
                Last Modified: %s
                
                Note: This appears to be a binary file. Content cannot be displayed as text.
                """, 
                bytes.length,
                filePath.getFileName(),
                formatBytes(fileSize),
                Files.getLastModifiedTime(filePath)
            );
        }
    }
    
    /**
     * 确定MIME类型
     */
    private String determineMimeType(Path filePath) {
        String fileName = filePath.getFileName().toString().toLowerCase();
        
        if (fileName.endsWith(".txt")) return "text/plain";
        if (fileName.endsWith(".md")) return "text/markdown";
        if (fileName.endsWith(".java")) return "text/x-java-source";
        if (fileName.endsWith(".xml")) return "application/xml";
        if (fileName.endsWith(".json")) return "application/json";
        if (fileName.endsWith(".yml") || fileName.endsWith(".yaml")) return "application/x-yaml";
        if (fileName.endsWith(".properties")) return "text/x-java-properties";
        if (fileName.endsWith(".html") || fileName.endsWith(".htm")) return "text/html";
        if (fileName.endsWith(".css")) return "text/css";
        if (fileName.endsWith(".js")) return "application/javascript";
        if (fileName.endsWith(".sql")) return "application/sql";
        if (fileName.endsWith(".sh")) return "application/x-sh";
        if (fileName.endsWith(".bat")) return "application/x-bat";
        if (fileName.endsWith(".log")) return "text/plain";
        
        // 尝试使用系统方法检测
        try {
            String detectedType = Files.probeContentType(filePath);
            if (detectedType != null) {
                return detectedType;
            }
        } catch (Exception e) {
            logger.debug("Failed to probe content type for: {}", filePath, e);
        }
        
        return "application/octet-stream";
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "N/A";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
}

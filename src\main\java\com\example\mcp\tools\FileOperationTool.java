package com.example.mcp.tools;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.CallToolResult;
import io.modelcontextprotocol.sdk.types.Tool;
import io.modelcontextprotocol.sdk.types.TextContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Map;

/**
 * 文件操作工具
 * 
 * 提供基本的文件读写功能
 */
public class FileOperationTool {
    
    private static final Logger logger = LoggerFactory.getLogger(FileOperationTool.class);
    
    // 安全限制：只允许在工作目录下操作文件
    private static final String WORK_DIR = System.getProperty("user.dir");
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    /**
     * 获取工具规范
     */
    public McpServerFeatures.AsyncToolSpecification getToolSpecification() {
        Tool tool = new Tool(
            "file_operation",
            "Read from or write to files (limited to working directory for security)",
            Map.of(
                "type", "object",
                "properties", Map.of(
                    "action", Map.of(
                        "type", "string",
                        "enum", new String[]{"read", "write", "append", "exists", "list"},
                        "description", "The file operation to perform"
                    ),
                    "path", Map.of(
                        "type", "string",
                        "description", "Relative path to the file (within working directory)"
                    ),
                    "content", Map.of(
                        "type", "string",
                        "description", "Content to write (required for write/append operations)"
                    )
                ),
                "required", new String[]{"action", "path"}
            )
        );
        
        return new McpServerFeatures.AsyncToolSpecification(tool, this::performFileOperation);
    }
    
    /**
     * 执行文件操作
     */
    private Mono<CallToolResult> performFileOperation(Object exchange, Map<String, Object> arguments) {
        return Mono.fromCallable(() -> {
            try {
                String action = (String) arguments.get("action");
                String relativePath = (String) arguments.get("path");
                String content = (String) arguments.get("content");
                
                // 安全检查：确保路径在工作目录内
                Path filePath = validatePath(relativePath);
                
                logger.debug("Performing file operation: {} on path: {}", action, filePath);
                
                String result = switch (action.toLowerCase()) {
                    case "read" -> readFile(filePath);
                    case "write" -> writeFile(filePath, content, false);
                    case "append" -> writeFile(filePath, content, true);
                    case "exists" -> checkFileExists(filePath);
                    case "list" -> listDirectory(filePath);
                    default -> throw new IllegalArgumentException("Unsupported action: " + action);
                };
                
                return new CallToolResult(
                    new TextContent("text", result),
                    false
                );
                
            } catch (Exception e) {
                logger.error("Error performing file operation", e);
                return new CallToolResult(
                    new TextContent("text", "Error: " + e.getMessage()),
                    true
                );
            }
        });
    }
    
    /**
     * 验证文件路径安全性
     */
    private Path validatePath(String relativePath) throws IOException {
        if (relativePath == null || relativePath.trim().isEmpty()) {
            throw new IllegalArgumentException("Path cannot be empty");
        }
        
        // 移除可能的路径遍历攻击
        if (relativePath.contains("..") || relativePath.startsWith("/")) {
            throw new SecurityException("Path traversal not allowed");
        }
        
        Path workDir = Paths.get(WORK_DIR);
        Path filePath = workDir.resolve(relativePath).normalize();
        
        // 确保解析后的路径仍在工作目录内
        if (!filePath.startsWith(workDir)) {
            throw new SecurityException("Access outside working directory not allowed");
        }
        
        return filePath;
    }
    
    /**
     * 读取文件内容
     */
    private String readFile(Path filePath) throws IOException {
        if (!Files.exists(filePath)) {
            throw new IOException("File does not exist: " + filePath);
        }
        
        if (!Files.isRegularFile(filePath)) {
            throw new IOException("Path is not a regular file: " + filePath);
        }
        
        long fileSize = Files.size(filePath);
        if (fileSize > MAX_FILE_SIZE) {
            throw new IOException("File too large (max " + MAX_FILE_SIZE + " bytes): " + fileSize);
        }
        
        String content = Files.readString(filePath);
        return "File content:\n" + content;
    }
    
    /**
     * 写入文件内容
     */
    private String writeFile(Path filePath, String content, boolean append) throws IOException {
        if (content == null) {
            throw new IllegalArgumentException("Content cannot be null for write/append operations");
        }
        
        // 确保父目录存在
        Path parentDir = filePath.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }
        
        if (append) {
            Files.writeString(filePath, content, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
            return "Content appended to file: " + filePath;
        } else {
            Files.writeString(filePath, content, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            return "Content written to file: " + filePath;
        }
    }
    
    /**
     * 检查文件是否存在
     */
    private String checkFileExists(Path filePath) {
        boolean exists = Files.exists(filePath);
        return "File exists: " + exists + " (path: " + filePath + ")";
    }
    
    /**
     * 列出目录内容
     */
    private String listDirectory(Path dirPath) throws IOException {
        if (!Files.exists(dirPath)) {
            return "Directory does not exist: " + dirPath;
        }
        
        if (!Files.isDirectory(dirPath)) {
            return "Path is not a directory: " + dirPath;
        }
        
        StringBuilder result = new StringBuilder("Directory contents:\n");
        Files.list(dirPath)
            .sorted()
            .forEach(path -> {
                String type = Files.isDirectory(path) ? "[DIR]" : "[FILE]";
                result.append(type).append(" ").append(path.getFileName()).append("\n");
            });
        
        return result.toString();
    }
}

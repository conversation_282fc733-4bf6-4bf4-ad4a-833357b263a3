#!/bin/bash

# Simple MCP Server 启动脚本
# 用于在Unix/Linux/macOS系统上启动MCP服务器

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
JAR_FILE="$PROJECT_DIR/target/simple-mcp-server-1.0.0.jar"
PID_FILE="$PROJECT_DIR/mcp-server.pid"
LOG_DIR="$PROJECT_DIR/logs"

# 默认配置
DEFAULT_PORT=8080
DEFAULT_PROFILE="default"
DEFAULT_JAVA_OPTS="-Xms256m -Xmx512m"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "Simple MCP Server 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -p, --port PORT        设置服务器端口 (默认: $DEFAULT_PORT)"
    echo "  -e, --profile PROFILE  设置Spring配置文件 (默认: $DEFAULT_PROFILE)"
    echo "  -j, --java-opts OPTS   设置Java虚拟机选项 (默认: $DEFAULT_JAVA_OPTS)"
    echo "  -d, --daemon           以守护进程模式运行"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                     # 使用默认配置启动"
    echo "  $0 -p 9090             # 在端口9090启动"
    echo "  $0 -e prod -d          # 以生产环境配置和守护进程模式启动"
    echo "  $0 -j \"-Xms512m -Xmx1g\" # 使用自定义JVM选项启动"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        print_message $RED "错误: 未找到Java运行环境"
        print_message $YELLOW "请安装Java 17或更高版本"
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    print_message $BLUE "Java版本: $java_version"
}

# 检查JAR文件
check_jar() {
    if [ ! -f "$JAR_FILE" ]; then
        print_message $RED "错误: 未找到JAR文件: $JAR_FILE"
        print_message $YELLOW "请先运行 'mvn clean package' 构建项目"
        exit 1
    fi
    print_message $BLUE "JAR文件: $JAR_FILE"
}

# 创建必要的目录
create_directories() {
    mkdir -p "$LOG_DIR"
    print_message $BLUE "日志目录: $LOG_DIR"
}

# 检查服务器是否已运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null 2>&1; then
            print_message $YELLOW "服务器已在运行 (PID: $pid)"
            print_message $YELLOW "如需重启，请先运行 './scripts/stop.sh'"
            exit 1
        else
            # PID文件存在但进程不存在，删除PID文件
            rm -f "$PID_FILE"
        fi
    fi
}

# 启动服务器
start_server() {
    local port=$1
    local profile=$2
    local java_opts=$3
    local daemon_mode=$4
    
    print_message $GREEN "正在启动Simple MCP Server..."
    print_message $BLUE "端口: $port"
    print_message $BLUE "配置文件: $profile"
    print_message $BLUE "Java选项: $java_opts"
    
    # 构建启动命令
    local cmd="java $java_opts -jar \"$JAR_FILE\""
    cmd="$cmd --server.port=$port"
    cmd="$cmd --spring.profiles.active=$profile"
    
    if [ "$daemon_mode" = true ]; then
        # 守护进程模式
        print_message $BLUE "模式: 守护进程"
        nohup bash -c "$cmd" > "$LOG_DIR/startup.log" 2>&1 &
        local pid=$!
        echo $pid > "$PID_FILE"
        
        # 等待服务器启动
        sleep 3
        if ps -p $pid > /dev/null 2>&1; then
            print_message $GREEN "服务器已启动 (PID: $pid)"
            print_message $BLUE "访问地址: http://localhost:$port"
            print_message $BLUE "健康检查: http://localhost:$port/api/health"
            print_message $BLUE "日志文件: $LOG_DIR/mcp-server.log"
        else
            print_message $RED "服务器启动失败"
            print_message $YELLOW "请检查日志文件: $LOG_DIR/startup.log"
            rm -f "$PID_FILE"
            exit 1
        fi
    else
        # 前台模式
        print_message $BLUE "模式: 前台运行"
        print_message $YELLOW "按 Ctrl+C 停止服务器"
        exec bash -c "$cmd"
    fi
}

# 解析命令行参数
PORT=$DEFAULT_PORT
PROFILE=$DEFAULT_PROFILE
JAVA_OPTS=$DEFAULT_JAVA_OPTS
DAEMON_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -e|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -j|--java-opts)
            JAVA_OPTS="$2"
            shift 2
            ;;
        -d|--daemon)
            DAEMON_MODE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_message $RED "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主执行流程
print_message $GREEN "Simple MCP Server 启动脚本"
print_message $GREEN "=========================="

check_java
check_jar
create_directories
check_running
start_server "$PORT" "$PROFILE" "$JAVA_OPTS" "$DAEMON_MODE"

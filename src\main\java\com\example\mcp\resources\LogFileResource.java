package com.example.mcp.resources;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.ReadResourceRequest;
import io.modelcontextprotocol.sdk.types.ReadResourceResult;
import io.modelcontextprotocol.sdk.types.Resource;
import io.modelcontextprotocol.sdk.types.TextResourceContents;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 日志文件资源
 * 
 * 提供应用日志文件访问功能
 */
public class LogFileResource {
    
    private static final Logger logger = LoggerFactory.getLogger(LogFileResource.class);
    
    private static final String RESOURCE_URI = "logs://application";
    private static final String LOG_DIR = "logs";
    private static final String DEFAULT_LOG_FILE = "mcp-server.log";
    private static final int MAX_LOG_LINES = 1000;
    private static final long MAX_LOG_SIZE = 10 * 1024 * 1024; // 10MB
    
    /**
     * 获取资源规范
     */
    public McpServerFeatures.AsyncResourceSpecification getResourceSpecification() {
        Resource resource = new Resource(
            RESOURCE_URI,
            "Application Logs",
            "Access to application log files",
            "text/plain"
        );
        
        return new McpServerFeatures.AsyncResourceSpecification(resource, this::readLogFile);
    }
    
    /**
     * 读取日志文件
     */
    private Mono<ReadResourceResult> readLogFile(Object exchange, ReadResourceRequest request) {
        return Mono.fromCallable(() -> {
            try {
                logger.debug("Reading log file resource");
                
                String logContent = readApplicationLogs();
                
                TextResourceContents contents = new TextResourceContents(
                    RESOURCE_URI,
                    "text/plain",
                    logContent
                );
                
                return new ReadResourceResult(contents);
                
            } catch (Exception e) {
                logger.error("Error reading log file", e);
                
                String errorContent = String.format("""
                    Error reading log file: %s
                    Timestamp: %s
                    
                    This error occurred while trying to access the application logs.
                    The log file might not exist yet or might not be accessible.
                    """, e.getMessage(), LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                
                TextResourceContents errorContents = new TextResourceContents(
                    RESOURCE_URI,
                    "text/plain",
                    errorContent
                );
                
                return new ReadResourceResult(errorContents);
            }
        });
    }
    
    /**
     * 读取应用日志
     */
    private String readApplicationLogs() throws IOException {
        Path logDir = Paths.get(LOG_DIR);
        Path logFile = logDir.resolve(DEFAULT_LOG_FILE);
        
        // 如果日志文件不存在，返回状态信息
        if (!Files.exists(logFile)) {
            return generateLogStatus(logDir, logFile);
        }
        
        // 检查文件大小
        long fileSize = Files.size(logFile);
        if (fileSize > MAX_LOG_SIZE) {
            return readTruncatedLog(logFile, fileSize);
        }
        
        // 读取完整日志文件
        List<String> lines = Files.readAllLines(logFile);
        
        StringBuilder content = new StringBuilder();
        content.append(String.format("Application Log File: %s\n", logFile.toAbsolutePath()));
        content.append(String.format("File Size: %s\n", formatBytes(fileSize)));
        content.append(String.format("Total Lines: %d\n", lines.size()));
        content.append(String.format("Last Modified: %s\n", Files.getLastModifiedTime(logFile)));
        content.append("=" .repeat(80)).append("\n\n");
        
        // 如果行数太多，只显示最后的部分
        if (lines.size() > MAX_LOG_LINES) {
            content.append(String.format("Showing last %d lines (total: %d lines)\n\n", MAX_LOG_LINES, lines.size()));
            lines = lines.subList(lines.size() - MAX_LOG_LINES, lines.size());
        }
        
        content.append(String.join("\n", lines));
        
        return content.toString();
    }
    
    /**
     * 生成日志状态信息
     */
    private String generateLogStatus(Path logDir, Path logFile) throws IOException {
        StringBuilder status = new StringBuilder();
        status.append("Log File Status\n");
        status.append("=" .repeat(50)).append("\n\n");
        
        status.append("Expected log file: ").append(logFile.toAbsolutePath()).append("\n");
        status.append("Log directory: ").append(logDir.toAbsolutePath()).append("\n");
        status.append("Log directory exists: ").append(Files.exists(logDir)).append("\n");
        status.append("Log file exists: ").append(Files.exists(logFile)).append("\n");
        status.append("Timestamp: ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append("\n\n");
        
        // 如果日志目录存在，列出其中的文件
        if (Files.exists(logDir) && Files.isDirectory(logDir)) {
            status.append("Files in log directory:\n");
            try {
                List<Path> files = Files.list(logDir)
                    .filter(Files::isRegularFile)
                    .sorted()
                    .collect(Collectors.toList());
                
                if (files.isEmpty()) {
                    status.append("  (no files found)\n");
                } else {
                    for (Path file : files) {
                        long size = Files.size(file);
                        status.append(String.format("  %s (%s)\n", file.getFileName(), formatBytes(size)));
                    }
                }
            } catch (IOException e) {
                status.append("  Error listing files: ").append(e.getMessage()).append("\n");
            }
        }
        
        status.append("\nNote: Log file will be created when the application starts logging.\n");
        status.append("Check the application configuration for logging settings.\n");
        
        return status.toString();
    }
    
    /**
     * 读取截断的日志（大文件）
     */
    private String readTruncatedLog(Path logFile, long fileSize) throws IOException {
        StringBuilder content = new StringBuilder();
        content.append(String.format("Application Log File: %s\n", logFile.toAbsolutePath()));
        content.append(String.format("File Size: %s (truncated - file too large)\n", formatBytes(fileSize)));
        content.append(String.format("Max Display Size: %s\n", formatBytes(MAX_LOG_SIZE)));
        content.append(String.format("Last Modified: %s\n", Files.getLastModifiedTime(logFile)));
        content.append("=" .repeat(80)).append("\n\n");
        
        content.append("WARNING: Log file is too large to display completely.\n");
        content.append(String.format("Showing last %d lines only.\n\n", MAX_LOG_LINES));
        
        // 读取文件的最后部分
        List<String> allLines = Files.readAllLines(logFile);
        List<String> lastLines = allLines.subList(
            Math.max(0, allLines.size() - MAX_LOG_LINES), 
            allLines.size()
        );
        
        content.append(String.join("\n", lastLines));
        
        return content.toString();
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "N/A";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
}

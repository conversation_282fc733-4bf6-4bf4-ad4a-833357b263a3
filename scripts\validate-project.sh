#!/bin/bash

# Simple MCP Server 项目验证脚本
# 验证项目结构和文件完整性

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查函数
check_file() {
    local file_path="$1"
    local description="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$PROJECT_DIR/$file_path" ]; then
        print_message $GREEN "✅ $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        print_message $RED "❌ $description (文件不存在: $file_path)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 检查目录
check_directory() {
    local dir_path="$1"
    local description="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -d "$PROJECT_DIR/$dir_path" ]; then
        print_message $GREEN "✅ $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        print_message $RED "❌ $description (目录不存在: $dir_path)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 检查文件内容
check_file_content() {
    local file_path="$1"
    local pattern="$2"
    local description="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$PROJECT_DIR/$file_path" ] && grep -q "$pattern" "$PROJECT_DIR/$file_path"; then
        print_message $GREEN "✅ $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        print_message $RED "❌ $description (内容检查失败)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 开始验证
print_message $BLUE "Simple MCP Server 项目验证"
print_message $BLUE "=========================="
echo

print_message $YELLOW "📁 检查项目结构..."

# 检查根目录文件
check_file "pom.xml" "Maven构建文件"
check_file "README.md" "项目说明文档"
check_file "Dockerfile" "Docker镜像配置"
check_file "docker-compose.yml" "Docker编排配置"
check_file ".gitignore" "Git忽略文件"

# 检查脚本目录
check_directory "scripts" "脚本目录"
check_file "scripts/start.sh" "Unix启动脚本"
check_file "scripts/stop.sh" "Unix停止脚本"
check_file "scripts/start.bat" "Windows启动脚本"
check_file "scripts/stop.bat" "Windows停止脚本"

echo

print_message $YELLOW "☕ 检查Java源代码..."

# 检查主要源代码目录
check_directory "src/main/java/com/example/mcp" "主源代码目录"

# 检查主应用类
check_file "src/main/java/com/example/mcp/SimpleMcpServerApplication.java" "主应用类"

# 检查配置类
check_directory "src/main/java/com/example/mcp/config" "配置包"
check_file "src/main/java/com/example/mcp/config/McpServerConfig.java" "MCP服务器配置"

# 检查服务层
check_directory "src/main/java/com/example/mcp/service" "服务层包"
check_file "src/main/java/com/example/mcp/service/McpServerService.java" "MCP服务器服务"
check_file "src/main/java/com/example/mcp/service/ToolService.java" "工具服务"
check_file "src/main/java/com/example/mcp/service/ResourceService.java" "资源服务"
check_file "src/main/java/com/example/mcp/service/PromptService.java" "提示服务"

# 检查控制器
check_directory "src/main/java/com/example/mcp/controller" "控制器包"
check_file "src/main/java/com/example/mcp/controller/HealthController.java" "健康检查控制器"

# 检查工具实现
check_directory "src/main/java/com/example/mcp/tools" "工具包"
check_file "src/main/java/com/example/mcp/tools/CalculatorTool.java" "计算器工具"
check_file "src/main/java/com/example/mcp/tools/FileOperationTool.java" "文件操作工具"
check_file "src/main/java/com/example/mcp/tools/SystemInfoTool.java" "系统信息工具"
check_file "src/main/java/com/example/mcp/tools/TextProcessorTool.java" "文本处理工具"

# 检查资源实现
check_directory "src/main/java/com/example/mcp/resources" "资源包"
check_file "src/main/java/com/example/mcp/resources/SystemStatusResource.java" "系统状态资源"
check_file "src/main/java/com/example/mcp/resources/ConfigurationResource.java" "配置信息资源"
check_file "src/main/java/com/example/mcp/resources/FileContentResource.java" "文件内容资源"
check_file "src/main/java/com/example/mcp/resources/LogFileResource.java" "日志文件资源"

# 检查提示实现
check_directory "src/main/java/com/example/mcp/prompts" "提示包"
check_file "src/main/java/com/example/mcp/prompts/CodeReviewPrompt.java" "代码审查提示"
check_file "src/main/java/com/example/mcp/prompts/DocumentationPrompt.java" "文档生成提示"
check_file "src/main/java/com/example/mcp/prompts/ErrorDiagnosisPrompt.java" "错误诊断提示"
check_file "src/main/java/com/example/mcp/prompts/PerformanceAnalysisPrompt.java" "性能分析提示"

echo

print_message $YELLOW "📋 检查配置文件..."

# 检查资源文件
check_directory "src/main/resources" "资源目录"
check_file "src/main/resources/application.yml" "应用配置文件"
check_file "src/main/resources/logback-spring.xml" "日志配置文件"

echo

print_message $YELLOW "🧪 检查测试代码..."

# 检查测试目录
check_directory "src/test/java/com/example/mcp" "测试源代码目录"
check_file "src/test/java/com/example/mcp/SimpleMcpServerApplicationTests.java" "应用测试类"

# 检查测试资源
check_directory "src/test/resources" "测试资源目录"
check_file "src/test/resources/application-test.yml" "测试配置文件"

echo

print_message $YELLOW "🔍 检查关键内容..."

# 检查Maven配置
check_file_content "pom.xml" "simple-mcp-server" "Maven项目名称"
check_file_content "pom.xml" "spring-boot-starter-webflux" "WebFlux依赖"
check_file_content "pom.xml" "mcp-sdk" "MCP SDK依赖"

# 检查应用配置
check_file_content "src/main/resources/application.yml" "server:" "服务器配置"
check_file_content "src/main/resources/application.yml" "mcp:" "MCP配置"

# 检查主应用类
check_file_content "src/main/java/com/example/mcp/SimpleMcpServerApplication.java" "@SpringBootApplication" "Spring Boot注解"

echo

# 显示结果统计
print_message $BLUE "验证结果统计"
print_message $BLUE "============"
echo "总检查项: $TOTAL_CHECKS"
print_message $GREEN "通过: $PASSED_CHECKS"
print_message $RED "失败: $FAILED_CHECKS"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo
    print_message $GREEN "🎉 项目验证通过！所有文件和结构都正确。"
    print_message $BLUE "项目已准备好进行构建和部署。"
    exit 0
else
    echo
    print_message $RED "⚠️  项目验证失败！发现 $FAILED_CHECKS 个问题。"
    print_message $YELLOW "请检查上述失败项并修复后重新验证。"
    exit 1
fi

package com.example.mcp.service;

import com.example.mcp.resources.ConfigurationResource;
import com.example.mcp.resources.FileContentResource;
import com.example.mcp.resources.LogFileResource;
import com.example.mcp.resources.SystemStatusResource;
import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 资源服务类
 * 
 * 管理所有MCP资源的注册和提供
 */
@Service
public class ResourceService {
    
    private static final Logger logger = LoggerFactory.getLogger(ResourceService.class);
    
    private final List<McpServerFeatures.AsyncResourceSpecification> resources;
    
    public ResourceService() {
        this.resources = new ArrayList<>();
        initializeResources();
    }
    
    /**
     * 初始化所有资源
     */
    private void initializeResources() {
        logger.info("Initializing MCP resources...");
        
        // 添加系统状态资源
        resources.add(new SystemStatusResource().getResourceSpecification());
        
        // 添加配置信息资源
        resources.add(new ConfigurationResource().getResourceSpecification());
        
        // 添加文件内容资源
        resources.add(new FileContentResource().getResourceSpecification());
        
        // 添加日志文件资源
        resources.add(new LogFileResource().getResourceSpecification());
        
        logger.info("Initialized {} resources", resources.size());
    }
    
    /**
     * 获取所有资源
     */
    public List<McpServerFeatures.AsyncResourceSpecification> getAllResources() {
        return new ArrayList<>(resources);
    }
    
    /**
     * 获取资源数量
     */
    public int getResourceCount() {
        return resources.size();
    }
    
    /**
     * 根据URI查找资源
     */
    public McpServerFeatures.AsyncResourceSpecification findResourceByUri(String uri) {
        return resources.stream()
            .filter(resource -> resource.resource().uri().equals(uri))
            .findFirst()
            .orElse(null);
    }
}

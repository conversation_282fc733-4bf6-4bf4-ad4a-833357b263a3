package com.example.mcp.prompts;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 代码审查提示模板
 * 
 * 提供代码质量分析和改进建议的提示模板
 */
public class CodeReviewPrompt {
    
    private static final Logger logger = LoggerFactory.getLogger(CodeReviewPrompt.class);
    
    /**
     * 获取提示规范
     */
    public McpServerFeatures.AsyncPromptSpecification getPromptSpecification() {
        Prompt prompt = new Prompt(
            "code_review",
            "Code Review and Quality Analysis",
            "Analyze code quality, identify issues, and suggest improvements",
            List.of(
                new PromptArgument(
                    "code",
                    "The source code to review",
                    true
                ),
                new PromptArgument(
                    "language",
                    "Programming language (e.g., java, python, javascript)",
                    false
                ),
                new PromptArgument(
                    "focus",
                    "Specific areas to focus on (e.g., security, performance, maintainability)",
                    false
                )
            )
        );
        
        return new McpServerFeatures.AsyncPromptSpecification(prompt, this::generateCodeReviewPrompt);
    }
    
    /**
     * 生成代码审查提示
     */
    private Mono<GetPromptResult> generateCodeReviewPrompt(Object exchange, GetPromptRequest request) {
        return Mono.fromCallable(() -> {
            try {
                Map<String, String> arguments = request.arguments();
                
                String code = arguments.get("code");
                String language = arguments.getOrDefault("language", "unknown");
                String focus = arguments.getOrDefault("focus", "general code quality");
                
                logger.debug("Generating code review prompt for language: {}, focus: {}", language, focus);
                
                if (code == null || code.trim().isEmpty()) {
                    throw new IllegalArgumentException("Code parameter is required and cannot be empty");
                }
                
                String promptText = generateReviewPromptText(code, language, focus);
                
                PromptMessage message = new PromptMessage(
                    "user",
                    new TextContent("text", promptText)
                );
                
                return new GetPromptResult(
                    "Code review prompt for " + language + " code focusing on " + focus,
                    List.of(message)
                );
                
            } catch (Exception e) {
                logger.error("Error generating code review prompt", e);
                
                PromptMessage errorMessage = new PromptMessage(
                    "user",
                    new TextContent("text", "Error generating code review prompt: " + e.getMessage())
                );
                
                return new GetPromptResult(
                    "Error in code review prompt",
                    List.of(errorMessage)
                );
            }
        });
    }
    
    /**
     * 生成代码审查提示文本
     */
    private String generateReviewPromptText(String code, String language, String focus) {
        return String.format("""
            Please perform a comprehensive code review of the following %s code, focusing on %s.
            
            **Review Guidelines:**
            
            1. **Code Quality & Style**
               - Check for consistent formatting and naming conventions
               - Identify any code smells or anti-patterns
               - Evaluate readability and maintainability
            
            2. **Security Considerations**
               - Look for potential security vulnerabilities
               - Check for proper input validation and sanitization
               - Identify any hardcoded secrets or sensitive information
            
            3. **Performance & Efficiency**
               - Analyze algorithmic complexity
               - Identify potential performance bottlenecks
               - Suggest optimizations where applicable
            
            4. **Best Practices**
               - Verify adherence to language-specific best practices
               - Check for proper error handling
               - Evaluate code organization and structure
            
            5. **Testing & Documentation**
               - Assess testability of the code
               - Check for adequate comments and documentation
               - Suggest areas that need better documentation
            
            **Code to Review:**
            
            ```%s
            %s
            ```
            
            **Please provide:**
            
            1. **Overall Assessment**: Brief summary of code quality (1-10 scale)
            2. **Issues Found**: List specific problems with line references where possible
            3. **Recommendations**: Concrete suggestions for improvement
            4. **Refactored Code**: If significant improvements are needed, provide refactored examples
            5. **Additional Notes**: Any other observations or suggestions
            
            Focus particularly on: %s
            
            Please be constructive and specific in your feedback, providing actionable recommendations.
            """, language, focus, language, code, focus);
    }
}

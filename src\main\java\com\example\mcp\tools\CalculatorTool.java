package com.example.mcp.tools;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.CallToolResult;
import io.modelcontextprotocol.sdk.types.Tool;
import io.modelcontextprotocol.sdk.types.TextContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 计算器工具
 * 
 * 提供基本的数学运算功能
 */
public class CalculatorTool {
    
    private static final Logger logger = LoggerFactory.getLogger(CalculatorTool.class);
    
    /**
     * 获取工具规范
     */
    public McpServerFeatures.AsyncToolSpecification getToolSpecification() {
        Tool tool = new Tool(
            "calculator",
            "Perform basic mathematical calculations",
            Map.of(
                "type", "object",
                "properties", Map.of(
                    "operation", Map.of(
                        "type", "string",
                        "enum", new String[]{"add", "subtract", "multiply", "divide", "power", "sqrt"},
                        "description", "The mathematical operation to perform"
                    ),
                    "a", Map.of(
                        "type", "number",
                        "description", "First number (required for all operations)"
                    ),
                    "b", Map.of(
                        "type", "number",
                        "description", "Second number (not required for sqrt operation)"
                    )
                ),
                "required", new String[]{"operation", "a"}
            )
        );
        
        return new McpServerFeatures.AsyncToolSpecification(tool, this::calculate);
    }
    
    /**
     * 执行计算
     */
    private Mono<CallToolResult> calculate(Object exchange, Map<String, Object> arguments) {
        return Mono.fromCallable(() -> {
            try {
                String operation = (String) arguments.get("operation");
                double a = getNumberValue(arguments.get("a"));
                Double b = arguments.containsKey("b") ? getNumberValue(arguments.get("b")) : null;
                
                logger.debug("Performing calculation: {} with a={}, b={}", operation, a, b);
                
                double result = switch (operation.toLowerCase()) {
                    case "add" -> {
                        if (b == null) throw new IllegalArgumentException("Second number required for addition");
                        yield a + b;
                    }
                    case "subtract" -> {
                        if (b == null) throw new IllegalArgumentException("Second number required for subtraction");
                        yield a - b;
                    }
                    case "multiply" -> {
                        if (b == null) throw new IllegalArgumentException("Second number required for multiplication");
                        yield a * b;
                    }
                    case "divide" -> {
                        if (b == null) throw new IllegalArgumentException("Second number required for division");
                        if (b == 0) throw new ArithmeticException("Division by zero");
                        yield a / b;
                    }
                    case "power" -> {
                        if (b == null) throw new IllegalArgumentException("Second number required for power operation");
                        yield Math.pow(a, b);
                    }
                    case "sqrt" -> {
                        if (a < 0) throw new ArithmeticException("Cannot calculate square root of negative number");
                        yield Math.sqrt(a);
                    }
                    default -> throw new IllegalArgumentException("Unsupported operation: " + operation);
                };
                
                String resultText = String.format("Result: %.6f", result);
                logger.debug("Calculation result: {}", resultText);
                
                return new CallToolResult(
                    new TextContent("text", resultText),
                    false
                );
                
            } catch (Exception e) {
                logger.error("Error performing calculation", e);
                return new CallToolResult(
                    new TextContent("text", "Error: " + e.getMessage()),
                    true
                );
            }
        });
    }
    
    /**
     * 从对象中提取数值
     */
    private double getNumberValue(Object value) {
        if (value instanceof Number number) {
            return number.doubleValue();
        } else if (value instanceof String str) {
            try {
                return Double.parseDouble(str);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid number format: " + str);
            }
        } else {
            throw new IllegalArgumentException("Expected number, got: " + value.getClass().getSimpleName());
        }
    }
}

@echo off
setlocal enabledelayedexpansion

REM Simple MCP Server Windows启动脚本
REM 用于在Windows系统上启动MCP服务器

REM 脚本配置
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..
set JAR_FILE=%PROJECT_DIR%\target\simple-mcp-server-1.0.0.jar
set PID_FILE=%PROJECT_DIR%\mcp-server.pid
set LOG_DIR=%PROJECT_DIR%\logs

REM 默认配置
set DEFAULT_PORT=8080
set DEFAULT_PROFILE=default
set DEFAULT_JAVA_OPTS=-Xms256m -Xmx512m

REM 初始化变量
set PORT=%DEFAULT_PORT%
set PROFILE=%DEFAULT_PROFILE%
set JAVA_OPTS=%DEFAULT_JAVA_OPTS%
set DAEMON_MODE=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :check_environment
if "%~1"=="-p" (
    set PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-e" (
    set PROFILE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--profile" (
    set PROFILE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-j" (
    set JAVA_OPTS=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--java-opts" (
    set JAVA_OPTS=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-d" (
    set DAEMON_MODE=true
    shift
    goto :parse_args
)
if "%~1"=="--daemon" (
    set DAEMON_MODE=true
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help

echo 错误: 未知选项 %~1
goto :show_help

:show_help
echo Simple MCP Server Windows启动脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -p, --port PORT        设置服务器端口 (默认: %DEFAULT_PORT%)
echo   -e, --profile PROFILE  设置Spring配置文件 (默认: %DEFAULT_PROFILE%)
echo   -j, --java-opts OPTS   设置Java虚拟机选项 (默认: %DEFAULT_JAVA_OPTS%)
echo   -d, --daemon           以后台模式运行
echo   -h, --help             显示此帮助信息
echo.
echo 示例:
echo   %~nx0                     # 使用默认配置启动
echo   %~nx0 -p 9090             # 在端口9090启动
echo   %~nx0 -e prod -d          # 以生产环境配置和后台模式启动
echo   %~nx0 -j "-Xms512m -Xmx1g" # 使用自定义JVM选项启动
goto :end

:check_environment
echo Simple MCP Server Windows启动脚本
echo ==================================

REM 检查Java环境
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java运行环境
    echo 请安装Java 17或更高版本
    exit /b 1
)

for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr /i version') do (
    set JAVA_VERSION=%%i
    goto :java_version_found
)
:java_version_found
echo Java版本: %JAVA_VERSION%

REM 检查JAR文件
if not exist "%JAR_FILE%" (
    echo 错误: 未找到JAR文件: %JAR_FILE%
    echo 请先运行 'mvn clean package' 构建项目
    exit /b 1
)
echo JAR文件: %JAR_FILE%

REM 创建必要的目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
echo 日志目录: %LOG_DIR%

REM 检查服务器是否已运行
if exist "%PID_FILE%" (
    set /p EXISTING_PID=<"%PID_FILE%"
    tasklist /FI "PID eq !EXISTING_PID!" 2>nul | find /I "java.exe" >nul
    if not errorlevel 1 (
        echo 服务器已在运行 (PID: !EXISTING_PID!)
        echo 如需重启，请先运行 'scripts\stop.bat'
        exit /b 1
    ) else (
        REM PID文件存在但进程不存在，删除PID文件
        del "%PID_FILE%" >nul 2>&1
    )
)

:start_server
echo 正在启动Simple MCP Server...
echo 端口: %PORT%
echo 配置文件: %PROFILE%
echo Java选项: %JAVA_OPTS%

REM 构建启动命令
set CMD=java %JAVA_OPTS% -jar "%JAR_FILE%" --server.port=%PORT% --spring.profiles.active=%PROFILE%

if "%DAEMON_MODE%"=="true" (
    REM 后台模式
    echo 模式: 后台运行
    start /B "" %CMD% > "%LOG_DIR%\startup.log" 2>&1
    
    REM 获取进程ID (Windows下比较复杂，这里简化处理)
    timeout /t 3 /nobreak >nul
    
    REM 检查服务是否启动成功
    netstat -an | find ":%PORT%" >nul
    if not errorlevel 1 (
        echo 服务器已启动
        echo 访问地址: http://localhost:%PORT%
        echo 健康检查: http://localhost:%PORT%/api/health
        echo 日志文件: %LOG_DIR%\mcp-server.log
    ) else (
        echo 服务器启动失败
        echo 请检查日志文件: %LOG_DIR%\startup.log
        exit /b 1
    )
) else (
    REM 前台模式
    echo 模式: 前台运行
    echo 按 Ctrl+C 停止服务器
    %CMD%
)

:end
endlocal

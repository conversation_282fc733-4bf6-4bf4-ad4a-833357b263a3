#!/bin/bash

# Simple MCP Server 停止脚本
# 用于停止运行中的MCP服务器

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PID_FILE="$PROJECT_DIR/mcp-server.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "Simple MCP Server 停止脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -f, --force    强制停止服务器 (使用SIGKILL)"
    echo "  -h, --help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0             # 正常停止服务器"
    echo "  $0 -f          # 强制停止服务器"
}

# 停止服务器
stop_server() {
    local force_mode=$1
    
    if [ ! -f "$PID_FILE" ]; then
        print_message $YELLOW "未找到PID文件，服务器可能未运行"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    
    if ! ps -p $pid > /dev/null 2>&1; then
        print_message $YELLOW "服务器进程不存在 (PID: $pid)"
        rm -f "$PID_FILE"
        return 0
    fi
    
    print_message $BLUE "正在停止Simple MCP Server (PID: $pid)..."
    
    if [ "$force_mode" = true ]; then
        # 强制停止
        print_message $YELLOW "使用强制模式停止服务器"
        kill -9 $pid
        sleep 1
    else
        # 正常停止
        print_message $BLUE "发送停止信号..."
        kill -TERM $pid
        
        # 等待进程正常退出
        local count=0
        local max_wait=30
        
        while ps -p $pid > /dev/null 2>&1 && [ $count -lt $max_wait ]; do
            sleep 1
            count=$((count + 1))
            if [ $((count % 5)) -eq 0 ]; then
                print_message $BLUE "等待服务器停止... ($count/$max_wait 秒)"
            fi
        done
        
        # 如果进程仍在运行，强制停止
        if ps -p $pid > /dev/null 2>&1; then
            print_message $YELLOW "正常停止超时，强制停止服务器"
            kill -9 $pid
            sleep 1
        fi
    fi
    
    # 验证进程是否已停止
    if ps -p $pid > /dev/null 2>&1; then
        print_message $RED "无法停止服务器进程 (PID: $pid)"
        exit 1
    else
        print_message $GREEN "服务器已成功停止"
        rm -f "$PID_FILE"
    fi
}

# 解析命令行参数
FORCE_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE_MODE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_message $RED "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主执行流程
print_message $GREEN "Simple MCP Server 停止脚本"
print_message $GREEN "=========================="

stop_server "$FORCE_MODE"

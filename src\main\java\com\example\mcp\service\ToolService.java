package com.example.mcp.service;

import com.example.mcp.tools.CalculatorTool;
import com.example.mcp.tools.FileOperationTool;
import com.example.mcp.tools.SystemInfoTool;
import com.example.mcp.tools.TextProcessorTool;
import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 工具服务类
 * 
 * 管理所有MCP工具的注册和提供
 */
@Service
public class ToolService {
    
    private static final Logger logger = LoggerFactory.getLogger(ToolService.class);
    
    private final List<McpServerFeatures.AsyncToolSpecification> tools;
    
    public ToolService() {
        this.tools = new ArrayList<>();
        initializeTools();
    }
    
    /**
     * 初始化所有工具
     */
    private void initializeTools() {
        logger.info("Initializing MCP tools...");
        
        // 添加计算器工具
        tools.add(new CalculatorTool().getToolSpecification());
        
        // 添加文件操作工具
        tools.add(new FileOperationTool().getToolSpecification());
        
        // 添加系统信息工具
        tools.add(new SystemInfoTool().getToolSpecification());
        
        // 添加文本处理工具
        tools.add(new TextProcessorTool().getToolSpecification());
        
        logger.info("Initialized {} tools", tools.size());
    }
    
    /**
     * 获取所有工具
     */
    public List<McpServerFeatures.AsyncToolSpecification> getAllTools() {
        return new ArrayList<>(tools);
    }
    
    /**
     * 获取工具数量
     */
    public int getToolCount() {
        return tools.size();
    }
    
    /**
     * 根据名称查找工具
     */
    public McpServerFeatures.AsyncToolSpecification findToolByName(String name) {
        return tools.stream()
            .filter(tool -> tool.tool().name().equals(name))
            .findFirst()
            .orElse(null);
    }
}

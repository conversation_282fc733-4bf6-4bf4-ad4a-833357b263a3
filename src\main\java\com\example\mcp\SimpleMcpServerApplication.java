package com.example.mcp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Simple MCP Server Application
 * 
 * 这是一个简单的Model Context Protocol (MCP) 服务器实现
 * 提供Tools、Resources和Prompts三大核心功能
 */
@SpringBootApplication
public class SimpleMcpServerApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleMcpServerApplication.class);

    public static void main(String[] args) {
        logger.info("Starting Simple MCP Server...");
        
        try {
            SpringApplication.run(SimpleMcpServerApplication.class, args);
            logger.info("Simple MCP Server started successfully!");
        } catch (Exception e) {
            logger.error("Failed to start Simple MCP Server", e);
            System.exit(1);
        }
    }
}

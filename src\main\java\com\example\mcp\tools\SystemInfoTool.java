package com.example.mcp.tools;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.CallToolResult;
import io.modelcontextprotocol.sdk.types.Tool;
import io.modelcontextprotocol.sdk.types.TextContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.Map;
import java.util.Properties;

/**
 * 系统信息工具
 * 
 * 提供系统和JVM相关信息
 */
public class SystemInfoTool {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemInfoTool.class);
    
    /**
     * 获取工具规范
     */
    public McpServerFeatures.AsyncToolSpecification getToolSpecification() {
        Tool tool = new Tool(
            "system_info",
            "Get system and JVM information",
            Map.of(
                "type", "object",
                "properties", Map.of(
                    "type", Map.of(
                        "type", "string",
                        "enum", new String[]{"os", "jvm", "memory", "runtime", "all"},
                        "description", "Type of system information to retrieve"
                    )
                ),
                "required", new String[]{"type"}
            )
        );
        
        return new McpServerFeatures.AsyncToolSpecification(tool, this::getSystemInfo);
    }
    
    /**
     * 获取系统信息
     */
    private Mono<CallToolResult> getSystemInfo(Object exchange, Map<String, Object> arguments) {
        return Mono.fromCallable(() -> {
            try {
                String type = (String) arguments.get("type");
                
                logger.debug("Getting system info of type: {}", type);
                
                String result = switch (type.toLowerCase()) {
                    case "os" -> getOperatingSystemInfo();
                    case "jvm" -> getJvmInfo();
                    case "memory" -> getMemoryInfo();
                    case "runtime" -> getRuntimeInfo();
                    case "all" -> getAllSystemInfo();
                    default -> throw new IllegalArgumentException("Unsupported info type: " + type);
                };
                
                return new CallToolResult(
                    new TextContent("text", result),
                    false
                );
                
            } catch (Exception e) {
                logger.error("Error getting system info", e);
                return new CallToolResult(
                    new TextContent("text", "Error: " + e.getMessage()),
                    true
                );
            }
        });
    }
    
    /**
     * 获取操作系统信息
     */
    private String getOperatingSystemInfo() {
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        
        StringBuilder info = new StringBuilder("Operating System Information:\n");
        info.append("Name: ").append(osBean.getName()).append("\n");
        info.append("Version: ").append(osBean.getVersion()).append("\n");
        info.append("Architecture: ").append(osBean.getArch()).append("\n");
        info.append("Available Processors: ").append(osBean.getAvailableProcessors()).append("\n");
        info.append("System Load Average: ").append(osBean.getSystemLoadAverage()).append("\n");
        
        return info.toString();
    }
    
    /**
     * 获取JVM信息
     */
    private String getJvmInfo() {
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        Properties systemProps = System.getProperties();
        
        StringBuilder info = new StringBuilder("JVM Information:\n");
        info.append("JVM Name: ").append(runtimeBean.getVmName()).append("\n");
        info.append("JVM Version: ").append(runtimeBean.getVmVersion()).append("\n");
        info.append("JVM Vendor: ").append(runtimeBean.getVmVendor()).append("\n");
        info.append("Java Version: ").append(systemProps.getProperty("java.version")).append("\n");
        info.append("Java Home: ").append(systemProps.getProperty("java.home")).append("\n");
        info.append("Class Path: ").append(runtimeBean.getClassPath()).append("\n");
        
        return info.toString();
    }
    
    /**
     * 获取内存信息
     */
    private String getMemoryInfo() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        Runtime runtime = Runtime.getRuntime();
        
        long heapUsed = memoryBean.getHeapMemoryUsage().getUsed();
        long heapMax = memoryBean.getHeapMemoryUsage().getMax();
        long nonHeapUsed = memoryBean.getNonHeapMemoryUsage().getUsed();
        long nonHeapMax = memoryBean.getNonHeapMemoryUsage().getMax();
        
        StringBuilder info = new StringBuilder("Memory Information:\n");
        info.append("Heap Memory Used: ").append(formatBytes(heapUsed)).append("\n");
        info.append("Heap Memory Max: ").append(formatBytes(heapMax)).append("\n");
        info.append("Non-Heap Memory Used: ").append(formatBytes(nonHeapUsed)).append("\n");
        info.append("Non-Heap Memory Max: ").append(formatBytes(nonHeapMax)).append("\n");
        info.append("Total Memory: ").append(formatBytes(runtime.totalMemory())).append("\n");
        info.append("Free Memory: ").append(formatBytes(runtime.freeMemory())).append("\n");
        info.append("Max Memory: ").append(formatBytes(runtime.maxMemory())).append("\n");
        
        return info.toString();
    }
    
    /**
     * 获取运行时信息
     */
    private String getRuntimeInfo() {
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        Properties systemProps = System.getProperties();
        
        StringBuilder info = new StringBuilder("Runtime Information:\n");
        info.append("Process ID: ").append(runtimeBean.getName().split("@")[0]).append("\n");
        info.append("Uptime: ").append(formatDuration(runtimeBean.getUptime())).append("\n");
        info.append("Start Time: ").append(new java.util.Date(runtimeBean.getStartTime())).append("\n");
        info.append("Working Directory: ").append(systemProps.getProperty("user.dir")).append("\n");
        info.append("User Name: ").append(systemProps.getProperty("user.name")).append("\n");
        info.append("User Home: ").append(systemProps.getProperty("user.home")).append("\n");
        info.append("Temp Directory: ").append(systemProps.getProperty("java.io.tmpdir")).append("\n");
        
        return info.toString();
    }
    
    /**
     * 获取所有系统信息
     */
    private String getAllSystemInfo() {
        StringBuilder allInfo = new StringBuilder();
        allInfo.append(getOperatingSystemInfo()).append("\n");
        allInfo.append(getJvmInfo()).append("\n");
        allInfo.append(getMemoryInfo()).append("\n");
        allInfo.append(getRuntimeInfo());
        
        return allInfo.toString();
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "N/A";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d days, %d hours, %d minutes", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d hours, %d minutes", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d minutes, %d seconds", minutes, seconds % 60);
        } else {
            return String.format("%d seconds", seconds);
        }
    }
}

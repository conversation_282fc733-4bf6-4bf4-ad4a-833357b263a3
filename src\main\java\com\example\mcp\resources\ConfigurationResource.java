package com.example.mcp.resources;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.ReadResourceRequest;
import io.modelcontextprotocol.sdk.types.ReadResourceResult;
import io.modelcontextprotocol.sdk.types.Resource;
import io.modelcontextprotocol.sdk.types.TextResourceContents;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * 配置信息资源
 * 
 * 提供应用配置和环境变量信息
 */
public class ConfigurationResource {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationResource.class);
    
    private static final String RESOURCE_URI = "config://application";
    
    /**
     * 获取资源规范
     */
    public McpServerFeatures.AsyncResourceSpecification getResourceSpecification() {
        Resource resource = new Resource(
            RESOURCE_URI,
            "Application Configuration",
            "Application configuration and environment information",
            "application/json"
        );
        
        return new McpServerFeatures.AsyncResourceSpecification(resource, this::readConfiguration);
    }
    
    /**
     * 读取配置信息
     */
    private Mono<ReadResourceResult> readConfiguration(Object exchange, ReadResourceRequest request) {
        return Mono.fromCallable(() -> {
            try {
                logger.debug("Reading configuration resource");
                
                String configJson = generateConfigurationJson();
                
                TextResourceContents contents = new TextResourceContents(
                    RESOURCE_URI,
                    "application/json",
                    configJson
                );
                
                return new ReadResourceResult(contents);
                
            } catch (Exception e) {
                logger.error("Error reading configuration", e);
                
                String errorJson = String.format("""
                    {
                        "error": "Failed to read configuration",
                        "message": "%s",
                        "timestamp": "%s"
                    }
                    """, e.getMessage(), LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                
                TextResourceContents errorContents = new TextResourceContents(
                    RESOURCE_URI,
                    "application/json",
                    errorJson
                );
                
                return new ReadResourceResult(errorContents);
            }
        });
    }
    
    /**
     * 生成配置信息JSON
     */
    private String generateConfigurationJson() {
        Properties systemProps = System.getProperties();
        
        // 获取系统属性
        String javaVersion = systemProps.getProperty("java.version");
        String javaVendor = systemProps.getProperty("java.vendor");
        String javaHome = systemProps.getProperty("java.home");
        String osName = systemProps.getProperty("os.name");
        String osVersion = systemProps.getProperty("os.version");
        String userDir = systemProps.getProperty("user.dir");
        String userName = systemProps.getProperty("user.name");
        String userHome = systemProps.getProperty("user.home");
        String tempDir = systemProps.getProperty("java.io.tmpdir");
        String fileSeparator = systemProps.getProperty("file.separator");
        String pathSeparator = systemProps.getProperty("path.separator");
        String lineSeparator = systemProps.getProperty("line.separator").replace("\n", "\\n").replace("\r", "\\r");
        
        // 获取环境变量（只显示一些安全的）
        String path = System.getenv("PATH");
        String javaOpts = System.getenv("JAVA_OPTS");
        String springProfilesActive = System.getenv("SPRING_PROFILES_ACTIVE");
        
        // 获取应用配置
        String serverPort = System.getProperty("server.port", "8080");
        String springAppName = System.getProperty("spring.application.name", "simple-mcp-server");
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        return String.format("""
            {
                "timestamp": "%s",
                "application": {
                    "name": "%s",
                    "version": "1.0.0",
                    "serverPort": "%s",
                    "profiles": "%s"
                },
                "java": {
                    "version": "%s",
                    "vendor": "%s",
                    "home": "%s",
                    "opts": "%s"
                },
                "system": {
                    "os": {
                        "name": "%s",
                        "version": "%s"
                    },
                    "user": {
                        "name": "%s",
                        "home": "%s",
                        "dir": "%s"
                    },
                    "paths": {
                        "temp": "%s",
                        "path": "%s"
                    },
                    "separators": {
                        "file": "%s",
                        "path": "%s",
                        "line": "%s"
                    }
                },
                "runtime": {
                    "availableProcessors": %d,
                    "maxMemory": %d,
                    "totalMemory": %d,
                    "freeMemory": %d
                }
            }
            """,
            timestamp,
            springAppName, serverPort, springProfilesActive != null ? springProfilesActive : "default",
            javaVersion, javaVendor, javaHome, javaOpts != null ? javaOpts : "",
            osName, osVersion,
            userName, userHome, userDir,
            tempDir, path != null ? path.substring(0, Math.min(path.length(), 200)) + "..." : "",
            fileSeparator, pathSeparator, lineSeparator,
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().maxMemory(),
            Runtime.getRuntime().totalMemory(),
            Runtime.getRuntime().freeMemory()
        );
    }
}

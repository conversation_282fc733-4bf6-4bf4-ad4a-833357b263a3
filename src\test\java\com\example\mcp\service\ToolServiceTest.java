package com.example.mcp.service;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工具服务测试
 */
class ToolServiceTest {

    private ToolService toolService;

    @BeforeEach
    void setUp() {
        toolService = new ToolService();
    }

    @Test
    void testGetAllTools() {
        List<McpServerFeatures.AsyncToolSpecification> tools = toolService.getAllTools();
        
        assertNotNull(tools);
        assertEquals(4, tools.size()); // 应该有4个工具
        
        // 验证工具名称
        List<String> toolNames = tools.stream()
            .map(tool -> tool.tool().name())
            .toList();
        
        assertTrue(toolNames.contains("calculator"));
        assertTrue(toolNames.contains("file_operation"));
        assertTrue(toolNames.contains("system_info"));
        assertTrue(toolNames.contains("text_processor"));
    }

    @Test
    void testGetToolCount() {
        int count = toolService.getToolCount();
        assertEquals(4, count);
    }

    @Test
    void testFindToolByName() {
        McpServerFeatures.AsyncToolSpecification calculatorTool = 
            toolService.findToolByName("calculator");
        
        assertNotNull(calculatorTool);
        assertEquals("calculator", calculatorTool.tool().name());
        assertEquals("Perform basic mathematical calculations", 
            calculatorTool.tool().description());
    }

    @Test
    void testFindNonExistentTool() {
        McpServerFeatures.AsyncToolSpecification nonExistentTool = 
            toolService.findToolByName("non_existent");
        
        assertNull(nonExistentTool);
    }

    @Test
    void testToolSpecifications() {
        List<McpServerFeatures.AsyncToolSpecification> tools = toolService.getAllTools();
        
        for (McpServerFeatures.AsyncToolSpecification tool : tools) {
            // 验证每个工具都有必要的属性
            assertNotNull(tool.tool().name());
            assertNotNull(tool.tool().description());
            assertNotNull(tool.tool().inputSchema());
            assertNotNull(tool.handler());
            
            // 验证工具名称不为空
            assertFalse(tool.tool().name().trim().isEmpty());
            assertFalse(tool.tool().description().trim().isEmpty());
        }
    }
}

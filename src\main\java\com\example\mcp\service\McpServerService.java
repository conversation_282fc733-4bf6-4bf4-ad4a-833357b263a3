package com.example.mcp.service;

import io.modelcontextprotocol.sdk.server.McpAsyncServer;
import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;

/**
 * MCP服务器核心服务类
 * 
 * 负责管理MCP服务器的生命周期，注册工具、资源和提示模板
 */
@Service
public class McpServerService {
    
    private static final Logger logger = LoggerFactory.getLogger(McpServerService.class);
    
    private final McpAsyncServer mcpServer;
    private final ToolService toolService;
    private final ResourceService resourceService;
    private final PromptService promptService;
    
    @Autowired
    public McpServerService(
            McpAsyncServer mcpServer,
            ToolService toolService,
            ResourceService resourceService,
            PromptService promptService) {
        this.mcpServer = mcpServer;
        this.toolService = toolService;
        this.resourceService = resourceService;
        this.promptService = promptService;
    }
    
    /**
     * 初始化MCP服务器，注册所有工具、资源和提示模板
     */
    @PostConstruct
    public void initialize() {
        logger.info("Initializing MCP Server Service...");
        
        try {
            // 注册工具
            registerTools();
            
            // 注册资源
            registerResources();
            
            // 注册提示模板
            registerPrompts();
            
            logger.info("MCP Server Service initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize MCP Server Service", e);
            throw new RuntimeException("MCP Server initialization failed", e);
        }
    }
    
    /**
     * 注册所有工具
     */
    private void registerTools() {
        logger.info("Registering MCP tools...");
        
        List<McpServerFeatures.AsyncToolSpecification> tools = toolService.getAllTools();
        
        for (McpServerFeatures.AsyncToolSpecification tool : tools) {
            mcpServer.addTool(tool)
                .doOnSuccess(v -> logger.debug("Tool registered: {}", tool.tool().name()))
                .doOnError(e -> logger.error("Failed to register tool: {}", tool.tool().name(), e))
                .subscribe();
        }
        
        logger.info("Registered {} tools", tools.size());
    }
    
    /**
     * 注册所有资源
     */
    private void registerResources() {
        logger.info("Registering MCP resources...");
        
        List<McpServerFeatures.AsyncResourceSpecification> resources = resourceService.getAllResources();
        
        for (McpServerFeatures.AsyncResourceSpecification resource : resources) {
            mcpServer.addResource(resource)
                .doOnSuccess(v -> logger.debug("Resource registered: {}", resource.resource().uri()))
                .doOnError(e -> logger.error("Failed to register resource: {}", resource.resource().uri(), e))
                .subscribe();
        }
        
        logger.info("Registered {} resources", resources.size());
    }
    
    /**
     * 注册所有提示模板
     */
    private void registerPrompts() {
        logger.info("Registering MCP prompts...");
        
        List<McpServerFeatures.AsyncPromptSpecification> prompts = promptService.getAllPrompts();
        
        for (McpServerFeatures.AsyncPromptSpecification prompt : prompts) {
            mcpServer.addPrompt(prompt)
                .doOnSuccess(v -> logger.debug("Prompt registered: {}", prompt.prompt().name()))
                .doOnError(e -> logger.error("Failed to register prompt: {}", prompt.prompt().name(), e))
                .subscribe();
        }
        
        logger.info("Registered {} prompts", prompts.size());
    }
    
    /**
     * 获取MCP服务器实例
     */
    public McpAsyncServer getMcpServer() {
        return mcpServer;
    }
    
    /**
     * 服务器关闭时的清理工作
     */
    @PreDestroy
    public void shutdown() {
        logger.info("Shutting down MCP Server Service...");
        
        try {
            mcpServer.close()
                .doOnSuccess(v -> logger.info("MCP Server closed successfully"))
                .doOnError(e -> logger.error("Error closing MCP Server", e))
                .block(); // 阻塞等待关闭完成
                
        } catch (Exception e) {
            logger.error("Error during MCP Server shutdown", e);
        }
    }
    
    /**
     * 检查服务器状态
     */
    public boolean isHealthy() {
        try {
            // 这里可以添加更复杂的健康检查逻辑
            return mcpServer != null;
        } catch (Exception e) {
            logger.error("Health check failed", e);
            return false;
        }
    }
    
    /**
     * 获取服务器统计信息
     */
    public ServerStats getServerStats() {
        return new ServerStats(
            toolService.getToolCount(),
            resourceService.getResourceCount(),
            promptService.getPromptCount()
        );
    }
    
    /**
     * 服务器统计信息记录类
     */
    public record ServerStats(
        int toolCount,
        int resourceCount,
        int promptCount
    ) {}
}

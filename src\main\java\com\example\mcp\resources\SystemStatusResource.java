package com.example.mcp.resources;

import io.modelcontextprotocol.sdk.server.features.McpServerFeatures;
import io.modelcontextprotocol.sdk.types.ReadResourceRequest;
import io.modelcontextprotocol.sdk.types.ReadResourceResult;
import io.modelcontextprotocol.sdk.types.Resource;
import io.modelcontextprotocol.sdk.types.TextResourceContents;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 系统状态资源
 * 
 * 提供实时的系统状态信息
 */
public class SystemStatusResource {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemStatusResource.class);
    
    private static final String RESOURCE_URI = "system://status";
    
    /**
     * 获取资源规范
     */
    public McpServerFeatures.AsyncResourceSpecification getResourceSpecification() {
        Resource resource = new Resource(
            RESOURCE_URI,
            "System Status",
            "Real-time system status and performance metrics",
            "application/json"
        );
        
        return new McpServerFeatures.AsyncResourceSpecification(resource, this::readSystemStatus);
    }
    
    /**
     * 读取系统状态
     */
    private Mono<ReadResourceResult> readSystemStatus(Object exchange, ReadResourceRequest request) {
        return Mono.fromCallable(() -> {
            try {
                logger.debug("Reading system status resource");
                
                String statusJson = generateSystemStatusJson();
                
                TextResourceContents contents = new TextResourceContents(
                    RESOURCE_URI,
                    "application/json",
                    statusJson
                );
                
                return new ReadResourceResult(contents);
                
            } catch (Exception e) {
                logger.error("Error reading system status", e);
                
                String errorJson = String.format("""
                    {
                        "error": "Failed to read system status",
                        "message": "%s",
                        "timestamp": "%s"
                    }
                    """, e.getMessage(), LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                
                TextResourceContents errorContents = new TextResourceContents(
                    RESOURCE_URI,
                    "application/json",
                    errorJson
                );
                
                return new ReadResourceResult(errorContents);
            }
        });
    }
    
    /**
     * 生成系统状态JSON
     */
    private String generateSystemStatusJson() {
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        Runtime runtime = Runtime.getRuntime();
        
        // 获取系统信息
        String osName = osBean.getName();
        String osVersion = osBean.getVersion();
        String osArch = osBean.getArch();
        int availableProcessors = osBean.getAvailableProcessors();
        double systemLoadAverage = osBean.getSystemLoadAverage();
        
        // 获取内存信息
        long heapUsed = memoryBean.getHeapMemoryUsage().getUsed();
        long heapMax = memoryBean.getHeapMemoryUsage().getMax();
        long nonHeapUsed = memoryBean.getNonHeapMemoryUsage().getUsed();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        
        // 获取运行时信息
        long uptime = runtimeBean.getUptime();
        String jvmName = runtimeBean.getVmName();
        String jvmVersion = runtimeBean.getVmVersion();
        
        // 计算内存使用率
        double heapUsagePercent = (double) heapUsed / heapMax * 100;
        double totalMemoryUsagePercent = (double) (totalMemory - freeMemory) / totalMemory * 100;
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        return String.format("""
            {
                "timestamp": "%s",
                "system": {
                    "os": {
                        "name": "%s",
                        "version": "%s",
                        "architecture": "%s",
                        "availableProcessors": %d,
                        "systemLoadAverage": %.2f
                    },
                    "jvm": {
                        "name": "%s",
                        "version": "%s",
                        "uptime": %d,
                        "uptimeFormatted": "%s"
                    }
                },
                "memory": {
                    "heap": {
                        "used": %d,
                        "max": %d,
                        "usagePercent": %.2f,
                        "usedFormatted": "%s",
                        "maxFormatted": "%s"
                    },
                    "nonHeap": {
                        "used": %d,
                        "usedFormatted": "%s"
                    },
                    "runtime": {
                        "total": %d,
                        "free": %d,
                        "max": %d,
                        "usagePercent": %.2f,
                        "totalFormatted": "%s",
                        "freeFormatted": "%s",
                        "maxFormatted": "%s"
                    }
                },
                "status": "healthy"
            }
            """,
            timestamp,
            osName, osVersion, osArch, availableProcessors, systemLoadAverage,
            jvmName, jvmVersion, uptime, formatDuration(uptime),
            heapUsed, heapMax, heapUsagePercent, formatBytes(heapUsed), formatBytes(heapMax),
            nonHeapUsed, formatBytes(nonHeapUsed),
            totalMemory, freeMemory, maxMemory, totalMemoryUsagePercent,
            formatBytes(totalMemory), formatBytes(freeMemory), formatBytes(maxMemory)
        );
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "N/A";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d days, %d hours, %d minutes", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d hours, %d minutes", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d minutes, %d seconds", minutes, seconds % 60);
        } else {
            return String.format("%d seconds", seconds);
        }
    }
}

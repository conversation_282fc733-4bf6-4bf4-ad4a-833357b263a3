server:
  port: 8080
  
spring:
  application:
    name: simple-mcp-server
  
  # WebFlux配置
  webflux:
    base-path: /api

# MCP服务器配置
mcp:
  server:
    name: "Simple MCP Server"
    version: "1.0.0"
    description: "A simple Model Context Protocol server implementation"
    
    # 服务器能力配置
    capabilities:
      tools: true
      resources: true
      prompts: true
      logging: true
      
    # 传输配置
    transport:
      type: "sse"  # Server-Sent Events
      endpoint: "/mcp/message"
      
# 日志配置
logging:
  level:
    com.example.mcp: DEBUG
    io.modelcontextprotocol: DEBUG
    org.springframework.web: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/mcp-server.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

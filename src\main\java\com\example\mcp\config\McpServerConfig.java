package com.example.mcp.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.modelcontextprotocol.sdk.server.McpServer;
import io.modelcontextprotocol.sdk.server.McpAsyncServer;
import io.modelcontextprotocol.sdk.server.transport.McpServerTransportProvider;
import io.modelcontextprotocol.sdk.server.transport.sse.WebFluxSseServerTransportProvider;
import io.modelcontextprotocol.sdk.types.ServerCapabilities;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

/**
 * MCP服务器配置类
 * 
 * 配置MCP服务器的传输层、能力和路由
 */
@Configuration
public class McpServerConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(McpServerConfig.class);
    
    @Value("${mcp.server.name:Simple MCP Server}")
    private String serverName;
    
    @Value("${mcp.server.version:1.0.0}")
    private String serverVersion;
    
    @Value("${mcp.transport.endpoint:/mcp/message}")
    private String messageEndpoint;

    /**
     * 创建ObjectMapper Bean用于JSON序列化
     */
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    /**
     * 创建WebFlux SSE传输提供者
     */
    @Bean
    public WebFluxSseServerTransportProvider sseServerTransportProvider(ObjectMapper objectMapper) {
        logger.info("Creating WebFlux SSE Server Transport Provider with endpoint: {}", messageEndpoint);
        return new WebFluxSseServerTransportProvider(objectMapper, messageEndpoint);
    }

    /**
     * 创建路由函数用于Spring WebFlux
     */
    @Bean
    public RouterFunction<ServerResponse> mcpRouterFunction(WebFluxSseServerTransportProvider transportProvider) {
        logger.info("Creating MCP router function");
        return transportProvider.getRouterFunction();
    }

    /**
     * 配置服务器能力
     */
    @Bean
    public ServerCapabilities serverCapabilities() {
        return ServerCapabilities.builder()
            .resources(false, true)  // 支持资源，启用列表变更通知
            .tools(true)            // 支持工具，启用列表变更通知
            .prompts(true)          // 支持提示，启用列表变更通知
            .logging()              // 启用日志支持
            .build();
    }

    /**
     * 创建异步MCP服务器
     */
    @Bean
    public McpAsyncServer mcpAsyncServer(
            McpServerTransportProvider transportProvider,
            ServerCapabilities capabilities) {
        
        logger.info("Creating MCP Async Server: {} v{}", serverName, serverVersion);
        
        return McpServer.async(transportProvider)
            .serverInfo(serverName, serverVersion)
            .capabilities(capabilities)
            .build();
    }
}

package com.example.mcp.tools;

import io.modelcontextprotocol.sdk.types.CallToolResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文本处理工具测试
 */
class TextProcessorToolTest {

    private TextProcessorTool textProcessorTool;

    @BeforeEach
    void setUp() {
        textProcessorTool = new TextProcessorTool();
    }

    @Test
    void testCountOperation() {
        String testText = "Hello World!\nThis is a test.";
        Map<String, Object> arguments = Map.of(
            "operation", "count",
            "text", testText
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                String content = toolResult.content().get(0).text();
                assertTrue(content.contains("Characters: 28"));
                assertTrue(content.contains("Words: 5"));
                assertTrue(content.contains("Lines: 2"));
            })
            .verifyComplete();
    }

    @Test
    void testTransformUppercase() {
        Map<String, Object> arguments = Map.of(
            "operation", "transform",
            "text", "hello world",
            "options", Map.of("transform_type", "uppercase")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("HELLO WORLD"));
            })
            .verifyComplete();
    }

    @Test
    void testTransformLowercase() {
        Map<String, Object> arguments = Map.of(
            "operation", "transform",
            "text", "HELLO WORLD",
            "options", Map.of("transform_type", "lowercase")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("hello world"));
            })
            .verifyComplete();
    }

    @Test
    void testTransformTitle() {
        Map<String, Object> arguments = Map.of(
            "operation", "transform",
            "text", "hello world test",
            "options", Map.of("transform_type", "title")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Hello World Test"));
            })
            .verifyComplete();
    }

    @Test
    void testTransformReverse() {
        Map<String, Object> arguments = Map.of(
            "operation", "transform",
            "text", "hello",
            "options", Map.of("transform_type", "reverse")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("olleh"));
            })
            .verifyComplete();
    }

    @Test
    void testSearchOperation() {
        String testText = "Hello World!\nThis is a test.\nHello again!";
        Map<String, Object> arguments = Map.of(
            "operation", "search",
            "text", testText,
            "options", Map.of("pattern", "Hello")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                String content = toolResult.content().get(0).text();
                assertTrue(content.contains("Matches found: 2"));
                assertTrue(content.contains("Line 1: Hello World!"));
                assertTrue(content.contains("Line 3: Hello again!"));
            })
            .verifyComplete();
    }

    @Test
    void testValidateEmail() {
        Map<String, Object> arguments = Map.of(
            "operation", "validate",
            "text", "<EMAIL>",
            "options", Map.of("validation_type", "email")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Valid: Yes"));
            })
            .verifyComplete();
    }

    @Test
    void testValidateInvalidEmail() {
        Map<String, Object> arguments = Map.of(
            "operation", "validate",
            "text", "invalid-email",
            "options", Map.of("validation_type", "email")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Valid: No"));
            })
            .verifyComplete();
    }

    @Test
    void testValidateUrl() {
        Map<String, Object> arguments = Map.of(
            "operation", "validate",
            "text", "https://www.example.com",
            "options", Map.of("validation_type", "url")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Valid: Yes"));
            })
            .verifyComplete();
    }

    @Test
    void testValidateJson() {
        Map<String, Object> arguments = Map.of(
            "operation", "validate",
            "text", "{\"name\": \"test\", \"value\": 123}",
            "options", Map.of("validation_type", "json")
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertFalse(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Valid: Yes"));
            })
            .verifyComplete();
    }

    @Test
    void testInvalidOperation() {
        Map<String, Object> arguments = Map.of(
            "operation", "invalid",
            "text", "test"
        );

        Mono<CallToolResult> result = textProcessorTool.getToolSpecification()
            .handler()
            .apply(null, arguments);

        StepVerifier.create(result)
            .assertNext(toolResult -> {
                assertTrue(toolResult.isError());
                assertTrue(toolResult.content().get(0).text().contains("Unsupported operation"));
            })
            .verifyComplete();
    }
}

@echo off
setlocal enabledelayedexpansion

REM Simple MCP Server 项目验证脚本 (Windows版本)
REM 验证项目结构和文件完整性

set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

REM 计数器
set TOTAL_CHECKS=0
set PASSED_CHECKS=0
set FAILED_CHECKS=0

echo Simple MCP Server 项目验证
echo ==========================
echo.

echo 📁 检查项目结构...

REM 检查根目录文件
call :check_file "pom.xml" "Maven构建文件"
call :check_file "README.md" "项目说明文档"
call :check_file "Dockerfile" "Docker镜像配置"
call :check_file "docker-compose.yml" "Docker编排配置"
call :check_file ".gitignore" "Git忽略文件"

REM 检查脚本目录
call :check_directory "scripts" "脚本目录"
call :check_file "scripts\start.sh" "Unix启动脚本"
call :check_file "scripts\stop.sh" "Unix停止脚本"
call :check_file "scripts\start.bat" "Windows启动脚本"
call :check_file "scripts\stop.bat" "Windows停止脚本"

echo.
echo ☕ 检查Java源代码...

REM 检查主要源代码目录
call :check_directory "src\main\java\com\example\mcp" "主源代码目录"

REM 检查主应用类
call :check_file "src\main\java\com\example\mcp\SimpleMcpServerApplication.java" "主应用类"

REM 检查配置类
call :check_directory "src\main\java\com\example\mcp\config" "配置包"
call :check_file "src\main\java\com\example\mcp\config\McpServerConfig.java" "MCP服务器配置"

REM 检查服务层
call :check_directory "src\main\java\com\example\mcp\service" "服务层包"
call :check_file "src\main\java\com\example\mcp\service\McpServerService.java" "MCP服务器服务"
call :check_file "src\main\java\com\example\mcp\service\ToolService.java" "工具服务"
call :check_file "src\main\java\com\example\mcp\service\ResourceService.java" "资源服务"
call :check_file "src\main\java\com\example\mcp\service\PromptService.java" "提示服务"

REM 检查控制器
call :check_directory "src\main\java\com\example\mcp\controller" "控制器包"
call :check_file "src\main\java\com\example\mcp\controller\HealthController.java" "健康检查控制器"

REM 检查工具实现
call :check_directory "src\main\java\com\example\mcp\tools" "工具包"
call :check_file "src\main\java\com\example\mcp\tools\CalculatorTool.java" "计算器工具"
call :check_file "src\main\java\com\example\mcp\tools\FileOperationTool.java" "文件操作工具"
call :check_file "src\main\java\com\example\mcp\tools\SystemInfoTool.java" "系统信息工具"
call :check_file "src\main\java\com\example\mcp\tools\TextProcessorTool.java" "文本处理工具"

REM 检查资源实现
call :check_directory "src\main\java\com\example\mcp\resources" "资源包"
call :check_file "src\main\java\com\example\mcp\resources\SystemStatusResource.java" "系统状态资源"
call :check_file "src\main\java\com\example\mcp\resources\ConfigurationResource.java" "配置信息资源"
call :check_file "src\main\java\com\example\mcp\resources\FileContentResource.java" "文件内容资源"
call :check_file "src\main\java\com\example\mcp\resources\LogFileResource.java" "日志文件资源"

REM 检查提示实现
call :check_directory "src\main\java\com\example\mcp\prompts" "提示包"
call :check_file "src\main\java\com\example\mcp\prompts\CodeReviewPrompt.java" "代码审查提示"
call :check_file "src\main\java\com\example\mcp\prompts\DocumentationPrompt.java" "文档生成提示"
call :check_file "src\main\java\com\example\mcp\prompts\ErrorDiagnosisPrompt.java" "错误诊断提示"
call :check_file "src\main\java\com\example\mcp\prompts\PerformanceAnalysisPrompt.java" "性能分析提示"

echo.
echo 📋 检查配置文件...

REM 检查资源文件
call :check_directory "src\main\resources" "资源目录"
call :check_file "src\main\resources\application.yml" "应用配置文件"
call :check_file "src\main\resources\logback-spring.xml" "日志配置文件"

echo.
echo 🧪 检查测试代码...

REM 检查测试目录
call :check_directory "src\test\java\com\example\mcp" "测试源代码目录"
call :check_file "src\test\java\com\example\mcp\SimpleMcpServerApplicationTests.java" "应用测试类"

REM 检查测试资源
call :check_directory "src\test\resources" "测试资源目录"
call :check_file "src\test\resources\application-test.yml" "测试配置文件"

echo.
echo 验证结果统计
echo ============
echo 总检查项: %TOTAL_CHECKS%
echo 通过: %PASSED_CHECKS%
echo 失败: %FAILED_CHECKS%

if %FAILED_CHECKS% equ 0 (
    echo.
    echo 🎉 项目验证通过！所有文件和结构都正确。
    echo 项目已准备好进行构建和部署。
    exit /b 0
) else (
    echo.
    echo ⚠️  项目验证失败！发现 %FAILED_CHECKS% 个问题。
    echo 请检查上述失败项并修复后重新验证。
    exit /b 1
)

:check_file
set /a TOTAL_CHECKS+=1
if exist "%PROJECT_DIR%\%~1" (
    echo ✅ %~2
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ %~2 (文件不存在: %~1)
    set /a FAILED_CHECKS+=1
)
goto :eof

:check_directory
set /a TOTAL_CHECKS+=1
if exist "%PROJECT_DIR%\%~1" (
    echo ✅ %~2
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ %~2 (目录不存在: %~1)
    set /a FAILED_CHECKS+=1
)
goto :eof

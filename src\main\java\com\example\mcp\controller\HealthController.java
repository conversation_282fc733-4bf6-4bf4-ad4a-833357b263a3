package com.example.mcp.controller;

import com.example.mcp.service.McpServerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 健康检查和服务器信息控制器
 */
@RestController
@RequestMapping("/api")
public class HealthController {
    
    private final McpServerService mcpServerService;
    
    @Autowired
    public HealthController(McpServerService mcpServerService) {
        this.mcpServerService = mcpServerService;
    }
    
    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<Map<String, Object>>> health() {
        return Mono.fromCallable(() -> {
            boolean isHealthy = mcpServerService.isHealthy();
            
            Map<String, Object> response = Map.of(
                "status", isHealthy ? "UP" : "DOWN",
                "timestamp", LocalDateTime.now(),
                "service", "Simple MCP Server"
            );
            
            return isHealthy ? 
                ResponseEntity.ok(response) : 
                ResponseEntity.status(503).body(response);
        });
    }
    
    /**
     * 服务器信息端点
     */
    @GetMapping("/info")
    public Mono<ResponseEntity<Map<String, Object>>> info() {
        return Mono.fromCallable(() -> {
            McpServerService.ServerStats stats = mcpServerService.getServerStats();
            
            Map<String, Object> response = Map.of(
                "name", "Simple MCP Server",
                "version", "1.0.0",
                "description", "A simple Model Context Protocol server implementation",
                "timestamp", LocalDateTime.now(),
                "statistics", Map.of(
                    "tools", stats.toolCount(),
                    "resources", stats.resourceCount(),
                    "prompts", stats.promptCount()
                )
            );
            
            return ResponseEntity.ok(response);
        });
    }
}

@echo off
setlocal enabledelayedexpansion

REM Simple MCP Server Windows停止脚本
REM 用于停止运行中的MCP服务器

REM 脚本配置
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..
set PID_FILE=%PROJECT_DIR%\mcp-server.pid

REM 初始化变量
set FORCE_MODE=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :stop_server
if "%~1"=="-f" (
    set FORCE_MODE=true
    shift
    goto :parse_args
)
if "%~1"=="--force" (
    set FORCE_MODE=true
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help

echo 错误: 未知选项 %~1
goto :show_help

:show_help
echo Simple MCP Server Windows停止脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -f, --force    强制停止服务器
echo   -h, --help     显示此帮助信息
echo.
echo 示例:
echo   %~nx0             # 正常停止服务器
echo   %~nx0 -f          # 强制停止服务器
goto :end

:stop_server
echo Simple MCP Server Windows停止脚本
echo ==================================

if not exist "%PID_FILE%" (
    echo 未找到PID文件，服务器可能未运行
    goto :end
)

set /p PID=<"%PID_FILE%"

REM 检查进程是否存在
tasklist /FI "PID eq %PID%" 2>nul | find /I "java.exe" >nul
if errorlevel 1 (
    echo 服务器进程不存在 (PID: %PID%)
    del "%PID_FILE%" >nul 2>&1
    goto :end
)

echo 正在停止Simple MCP Server (PID: %PID%)...

if "%FORCE_MODE%"=="true" (
    REM 强制停止
    echo 使用强制模式停止服务器
    taskkill /F /PID %PID% >nul 2>&1
    timeout /t 1 /nobreak >nul
) else (
    REM 正常停止
    echo 发送停止信号...
    taskkill /PID %PID% >nul 2>&1
    
    REM 等待进程正常退出
    set count=0
    set max_wait=30
    
    :wait_loop
    if !count! geq %max_wait% goto :force_stop
    
    tasklist /FI "PID eq %PID%" 2>nul | find /I "java.exe" >nul
    if errorlevel 1 goto :stopped
    
    timeout /t 1 /nobreak >nul
    set /a count+=1
    
    set /a remainder=!count! %% 5
    if !remainder! equ 0 (
        echo 等待服务器停止... (!count!/%max_wait% 秒)
    )
    goto :wait_loop
    
    :force_stop
    echo 正常停止超时，强制停止服务器
    taskkill /F /PID %PID% >nul 2>&1
    timeout /t 1 /nobreak >nul
)

:stopped
REM 验证进程是否已停止
tasklist /FI "PID eq %PID%" 2>nul | find /I "java.exe" >nul
if not errorlevel 1 (
    echo 无法停止服务器进程 (PID: %PID%)
    exit /b 1
) else (
    echo 服务器已成功停止
    del "%PID_FILE%" >nul 2>&1
)

:end
endlocal
